# PassChanger - Security-focused .gitignore

# Sensitive data directories
data/
logs/
*.key
*.enc

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Logs
*.log

# Temporary files
*.tmp
*.temp
temp/

# Database files (if not in data/)
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Configuration overrides (keep template)
config.local.yaml
.env.local

# Security scan results
security_scan_*

# Browser automation
chromedriver
geckodriver
*.crx
