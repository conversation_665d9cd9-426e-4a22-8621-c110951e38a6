# PassChanger Configuration
app:
  name: "PassChanger"
  version: "1.0.0"
  debug: true
  log_level: "INFO"

# Local LLM Configuration
llm:
  provider: "ollama"
  model: "deepseek-r1:32b"  # Change to your preferred local model
  base_url: "http://localhost:11434"
  timeout: 30
  max_tokens: 2048

# Database Configuration
database:
  type: "sqlite"
  path: "data/passchanger.db"
  backup_interval: 24  # hours

# Security Configuration
security:
  encryption_key_file: "data/encryption.key"
  password_length: 16
  password_complexity: true
  session_timeout: 3600  # seconds

# Leak Detection Configuration
leak_detection:
  enabled: true
  check_interval: 24  # hours
  sources:
    haveibeenpwned:
      enabled: true
      api_key: ""  # Optional, rate limited without key
      user_agent: "PassChanger-LeakDetector"

    darkweb:
      enabled: false  # Requires Tor setup
      tor_proxy: "socks5://127.0.0.1:9050"

    custom_searches:
      enabled: true
      search_engines: ["google", "bing", "duckduckgo"]
      max_results_per_engine: 10

# Monitoring Configuration
monitoring:
  email_alerts: false
  desktop_notifications: true
  log_file: "logs/passchanger.log"
  max_log_size: "10MB"

# Account Management
accounts:
  storage_file: "data/accounts.enc"
  categories:
    - "critical"    # Banking, email, etc.
    - "important"   # Social media, work accounts
    - "standard"    # Shopping, forums, etc.
    - "low"         # Newsletters, trials, etc.

# Password Change Agent
password_agent:
  enabled: false  # Will be enabled in Phase 2
  browser: "chromium"
  headless: false
  timeout: 30
  retry_attempts: 3

# Web Scraping
scraping:
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
  request_delay: 1  # seconds between requests
  max_retries: 3
  timeout: 10
