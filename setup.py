#!/usr/bin/env python3
"""
Setup script for PassChanger
Helps with initial setup and dependency installation
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_ollama():
    """Check if Ollama is installed and running"""
    print("\nChecking Ollama installation...")
    
    # Check if ollama command exists
    if not run_command("ollama --version", check=False):
        print("❌ Ollama not found")
        print("Please install Ollama from: https://ollama.ai/")
        return False
    
    print("✅ Ollama is installed")
    
    # Check if Ollama is running
    if not run_command("ollama list", check=False):
        print("⚠️  Ollama service might not be running")
        print("Try running: ollama serve")
        return False
    
    print("✅ Ollama service is running")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\nInstalling Python dependencies...")
    
    if not run_command(f"{sys.executable} -m pip install --upgrade pip"):
        print("❌ Failed to upgrade pip")
        return False
    
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt"):
        print("❌ Failed to install dependencies")
        return False
    
    print("✅ Dependencies installed successfully")
    return True

def setup_directories():
    """Create necessary directories"""
    print("\nSetting up directories...")
    
    directories = ['data', 'logs', 'tests']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def test_installation():
    """Test the installation"""
    print("\nTesting installation...")
    
    # Test basic imports
    try:
        import yaml
        import aiohttp
        import cryptography
        import ollama
        print("✅ All required packages can be imported")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test configuration loading
    try:
        with open('config.yaml', 'r') as f:
            yaml.safe_load(f)
        print("✅ Configuration file is valid")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Run basic tests
    if run_command(f"{sys.executable} tests/test_basic.py", check=False):
        print("✅ Basic tests passed")
    else:
        print("⚠️  Some basic tests failed (this might be OK)")
    
    return True

def suggest_ollama_models():
    """Suggest Ollama models to download"""
    print("\nRecommended Ollama models for PassChanger:")
    print("  • llama2 (7B) - Good balance of speed and capability")
    print("  • mistral (7B) - Fast and efficient")
    print("  • codellama (7B) - Good for technical analysis")
    print("  • llama2:13b - More capable but slower")
    
    print("\nTo download a model, run:")
    print("  ollama pull llama2")
    print("  ollama pull mistral")
    
    # Check what models are available
    result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
    if result.returncode == 0 and result.stdout.strip():
        print("\nCurrently installed models:")
        print(result.stdout)
    else:
        print("\nNo models currently installed.")
        print("You'll need to download at least one model before using PassChanger.")

def main():
    """Main setup function"""
    print("🔐 PassChanger Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check Ollama
    ollama_ok = check_ollama()
    
    # Setup directories
    if not setup_directories():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("⚠️  Installation test had issues, but you can try running the app")
    
    # Ollama model suggestions
    if ollama_ok:
        suggest_ollama_models()
    
    print("\n" + "=" * 50)
    print("🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Review and edit config.yaml if needed")
    print("2. Make sure you have an Ollama model downloaded")
    print("3. Run the application: python main.py")
    
    if not ollama_ok:
        print("\n⚠️  Remember to install and setup Ollama first!")

if __name__ == "__main__":
    main()
