#!/usr/bin/env python3
"""
Setup script for PassChanger
Helps with initial setup and dependency installation using uv
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check,
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_uv():
    """Check if uv is installed"""
    print("Checking uv installation...")

    if not run_command("uv --version", check=False):
        print("❌ uv not found")
        print("Please install uv from: https://github.com/astral-sh/uv")
        print("Quick install: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False

    print("✅ uv is installed")
    return True

def check_python_version():
    """Check if Python version is compatible"""
    # uv will handle Python version requirements
    print("✅ Python version will be managed by uv")
    return True

def check_ollama():
    """Check if Ollama is installed and running"""
    print("\nChecking Ollama installation...")

    # Check if ollama command exists
    if not run_command("ollama --version", check=False):
        print("❌ Ollama not found")
        print("Please install Ollama from: https://ollama.ai/")
        return False

    print("✅ Ollama is installed")

    # Check if Ollama is running
    if not run_command("ollama list", check=False):
        print("⚠️  Ollama service might not be running")
        print("Try running: ollama serve")
        return False

    print("✅ Ollama service is running")
    return True

def install_dependencies():
    """Install Python dependencies using uv"""
    print("\nInstalling Python dependencies with uv...")

    # Create virtual environment and install dependencies
    if not run_command("uv venv"):
        print("❌ Failed to create virtual environment")
        return False

    if not run_command("uv pip install -e ."):
        print("❌ Failed to install project dependencies")
        return False

    print("✅ Dependencies installed successfully")
    print("✅ Virtual environment created with uv")
    return True

def setup_directories():
    """Create necessary directories"""
    print("\nSetting up directories...")

    directories = ['data', 'logs', 'tests']

    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

    return True

def test_installation():
    """Test the installation"""
    print("\nTesting installation...")

    # Test basic imports using activated venv
    test_imports = [
        "source .venv/bin/activate && python -c 'import yaml; print(\"yaml OK\")'",
        "source .venv/bin/activate && python -c 'import aiohttp; print(\"aiohttp OK\")'",
        "source .venv/bin/activate && python -c 'import cryptography; print(\"cryptography OK\")'",
        "source .venv/bin/activate && python -c 'import ollama; print(\"ollama OK\")'",
    ]

    for test_cmd in test_imports:
        if run_command(test_cmd, check=False):
            print("✅ Import test passed")
        else:
            print(f"⚠️  Import test failed")

    # Test configuration loading
    try:
        import yaml
        with open('config.yaml', 'r') as f:
            yaml.safe_load(f)
        print("✅ Configuration file is valid")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

    # Run basic tests with activated venv
    if run_command("source .venv/bin/activate && python tests/test_basic.py", check=False):
        print("✅ Basic tests passed")
    else:
        print("⚠️  Some basic tests failed (this might be OK)")

    return True

def suggest_ollama_models():
    """Suggest Ollama models to download"""
    print("\nRecommended Ollama models for PassChanger:")
    print("  • llama2 (7B) - Good balance of speed and capability")
    print("  • mistral (7B) - Fast and efficient")
    print("  • codellama (7B) - Good for technical analysis")
    print("  • llama2:13b - More capable but slower")

    print("\nTo download a model, run:")
    print("  ollama pull llama2")
    print("  ollama pull mistral")

    # Check what models are available
    result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
    if result.returncode == 0 and result.stdout.strip():
        print("\nCurrently installed models:")
        print(result.stdout)
    else:
        print("\nNo models currently installed.")
        print("You'll need to download at least one model before using PassChanger.")

def main():
    """Main setup function"""
    print("🔐 PassChanger Setup (with uv)")
    print("=" * 50)

    # Check uv
    if not check_uv():
        sys.exit(1)

    # Check Python version (uv handles this)
    if not check_python_version():
        sys.exit(1)

    # Check Ollama
    ollama_ok = check_ollama()

    # Setup directories
    if not setup_directories():
        sys.exit(1)

    # Install dependencies
    if not install_dependencies():
        sys.exit(1)

    # Test installation
    if not test_installation():
        print("⚠️  Installation test had issues, but you can try running the app")

    # Ollama model suggestions
    if ollama_ok:
        suggest_ollama_models()

    print("\n" + "=" * 50)
    print("🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Review and edit config.yaml if needed")
    print("2. Make sure you have an Ollama model downloaded")
    print("3. Run the application: uv run python main.py")
    print("   Or activate the venv: source .venv/bin/activate && python main.py")

    if not ollama_ok:
        print("\n⚠️  Remember to install and setup Ollama first!")

if __name__ == "__main__":
    main()
