"""
Security utilities for PassChanger
Handles encryption, secure storage, and security operations
"""

import os
import logging
import hashlib
import secrets
from pathlib import Path
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

logger = logging.getLogger("PassChanger.Security")

class SecurityManager:
    def __init__(self, config: dict):
        self.config = config
        self.security_config = config['security']
        self.encryption_key_file = self.security_config['encryption_key_file']
        self.fernet = None
        
    async def initialize(self):
        """Initialize security manager"""
        try:
            # Ensure data directory exists
            Path(self.encryption_key_file).parent.mkdir(parents=True, exist_ok=True)
            
            # Load or generate encryption key
            self.fernet = await self._get_or_create_encryption_key()
            
            logger.info("Security manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize security manager: {e}")
            raise
    
    async def _get_or_create_encryption_key(self) -> <PERSON><PERSON><PERSON>:
        """Get existing encryption key or create a new one"""
        if os.path.exists(self.encryption_key_file):
            # Load existing key
            with open(self.encryption_key_file, 'rb') as f:
                key = f.read()
            logger.info("Loaded existing encryption key")
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(self.encryption_key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions
            os.chmod(self.encryption_key_file, 0o600)
            logger.info("Generated new encryption key")
        
        return Fernet(key)
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not self.fernet:
            raise Exception("Security manager not initialized")
        
        encrypted = self.fernet.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        if not self.fernet:
            raise Exception("Security manager not initialized")
        
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            raise
    
    def generate_secure_password(self, length: int = None, 
                                complexity: bool = None) -> str:
        """Generate a secure password"""
        if length is None:
            length = self.security_config['password_length']
        if complexity is None:
            complexity = self.security_config['password_complexity']
        
        if complexity:
            # Complex password with mixed characters
            chars = (
                'abcdefghijklmnopqrstuvwxyz'
                'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
                '0123456789'
                '!@#$%^&*()_+-=[]{}|;:,.<>?'
            )
        else:
            # Simple alphanumeric password
            chars = (
                'abcdefghijklmnopqrstuvwxyz'
                'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
                '0123456789'
            )
        
        password = ''.join(secrets.choice(chars) for _ in range(length))
        return password
    
    def hash_password(self, password: str) -> str:
        """Hash a password for storage (one-way)"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode(),
            salt.encode(),
            100000  # iterations
        )
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password_hash(self, password: str, stored_hash: str) -> bool:
        """Verify a password against its hash"""
        try:
            salt, hash_hex = stored_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode(),
                salt.encode(),
                100000
            )
            return password_hash.hex() == hash_hex
        except Exception:
            return False
    
    def sanitize_input(self, data: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        if not isinstance(data, str):
            return str(data)
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '|', '`', '$']
        sanitized = data
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()
    
    def validate_email(self, email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def generate_session_token(self) -> str:
        """Generate a secure session token"""
        return secrets.token_urlsafe(32)
    
    def secure_delete_file(self, file_path: str):
        """Securely delete a file by overwriting it"""
        try:
            if os.path.exists(file_path):
                # Get file size
                file_size = os.path.getsize(file_path)
                
                # Overwrite with random data multiple times
                with open(file_path, 'r+b') as f:
                    for _ in range(3):
                        f.seek(0)
                        f.write(os.urandom(file_size))
                        f.flush()
                        os.fsync(f.fileno())
                
                # Finally delete the file
                os.remove(file_path)
                logger.info(f"Securely deleted file: {file_path}")
        except Exception as e:
            logger.error(f"Failed to securely delete file {file_path}: {e}")
    
    def check_password_strength(self, password: str) -> Dict[str, Any]:
        """Check password strength locally"""
        score = 0
        issues = []
        suggestions = []
        
        # Length check
        if len(password) < 8:
            issues.append("Password too short")
            suggestions.append("Use at least 8 characters")
        elif len(password) >= 12:
            score += 25
        else:
            score += 15
        
        # Character variety checks
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(not c.isalnum() for c in password)
        
        char_types = sum([has_lower, has_upper, has_digit, has_special])
        
        if char_types >= 4:
            score += 30
        elif char_types >= 3:
            score += 20
        elif char_types >= 2:
            score += 10
        else:
            issues.append("Limited character variety")
            suggestions.append("Use uppercase, lowercase, numbers, and symbols")
        
        # Common patterns check
        common_patterns = ['123', 'abc', 'password', 'qwerty', '111', '000']
        if any(pattern in password.lower() for pattern in common_patterns):
            score -= 20
            issues.append("Contains common patterns")
            suggestions.append("Avoid common patterns and sequences")
        
        # Repetition check
        if len(set(password)) < len(password) * 0.6:
            score -= 15
            issues.append("Too many repeated characters")
            suggestions.append("Use more varied characters")
        
        # Final scoring
        score = max(0, min(100, score))
        
        if score >= 80:
            strength = "very_strong"
        elif score >= 60:
            strength = "strong"
        elif score >= 40:
            strength = "fair"
        elif score >= 20:
            strength = "weak"
        else:
            strength = "very_weak"
        
        return {
            'strength': strength,
            'score': score,
            'issues': issues,
            'suggestions': suggestions
        }
    
    def create_backup_codes(self, count: int = 10) -> list:
        """Generate backup codes for 2FA"""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') 
                          for _ in range(8))
            # Format as XXXX-XXXX
            formatted_code = f"{code[:4]}-{code[4:]}"
            codes.append(formatted_code)
        
        return codes
    
    async def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security-related events"""
        logger.info(f"Security event: {event_type} - {details}")
        
        # Here you could also send to external security monitoring
        # or integrate with SIEM systems if needed
