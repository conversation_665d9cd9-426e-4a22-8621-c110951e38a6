"""
AI Engine for PassChanger
Handles local LLM integration and AI-powered analysis
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
import aiohttp
import ollama

logger = logging.getLogger("PassChanger.AIEngine")

class AIEngine:
    def __init__(self, config: dict):
        self.config = config
        self.llm_config = config['llm']
        self.client = None
        self.model = self.llm_config['model']
        self.base_url = self.llm_config['base_url']

    async def initialize(self):
        """Initialize the AI engine"""
        try:
            # Test connection to Ollama
            await self._test_connection()
            logger.info(f"AI Engine initialized with model: {self.model}")
        except Exception as e:
            logger.error(f"Failed to initialize AI engine: {e}")
            raise

    async def _test_connection(self):
        """Test connection to local LLM"""
        try:
            # Try to list available models
            models = ollama.list()
            available_models = []

            # Handle different response formats
            if hasattr(models, 'models'):
                # New ollama library format
                available_models = [model.model for model in models.models]
            elif isinstance(models, dict) and 'models' in models:
                available_models = [model.get('name', model.get('model', '')) for model in models['models']]
            elif isinstance(models, list):
                available_models = [model.get('name', model.get('model', '')) for model in models]

            # Filter out empty names
            available_models = [name for name in available_models if name]

            logger.info(f"Available models: {available_models}")

            if self.model not in available_models:
                logger.warning(f"Model {self.model} not found. Available models: {available_models}")
                if available_models:
                    self.model = available_models[0]
                    logger.info(f"Switching to available model: {self.model}")
                else:
                    raise Exception("No models available in Ollama")

            # Test a simple query
            response = ollama.generate(
                model=self.model,
                prompt="Test connection. Respond with 'OK'.",
                options={'num_predict': 10}
            )

            if 'response' in response:
                logger.info("LLM connection test successful")
            else:
                raise Exception("Invalid response from LLM")

        except Exception as e:
            logger.error(f"LLM connection test failed: {e}")
            raise

    async def analyze_leak_data(self, data: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze potential leak data using AI

        Args:
            data: Raw data that might contain leaked information
            context: Context about what we're looking for (emails, usernames, etc.)

        Returns:
            Analysis results with risk assessment
        """
        try:
            prompt = self._build_leak_analysis_prompt(data, context)

            response = ollama.generate(
                model=self.model,
                prompt=prompt,
                options={
                    'temperature': 0.1,  # Low temperature for consistent analysis
                    'num_predict': 500
                }
            )

            analysis = self._parse_analysis_response(response['response'])
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing leak data: {e}")
            return {
                'risk_level': 'unknown',
                'confidence': 0.0,
                'findings': [],
                'recommendations': [],
                'error': str(e)
            }

    def _build_leak_analysis_prompt(self, data: str, context: Dict[str, Any]) -> str:
        """Build prompt for leak analysis"""
        user_emails = context.get('emails', [])
        user_usernames = context.get('usernames', [])

        prompt = f"""
You are a cybersecurity expert analyzing potential data leaks.

CONTEXT:
- User emails to monitor: {', '.join(user_emails)}
- User usernames to monitor: {', '.join(user_usernames)}

DATA TO ANALYZE:
{data[:2000]}  # Limit data size

TASK:
Analyze this data for potential security breaches involving the user's accounts.

RESPOND IN JSON FORMAT:
{{
    "risk_level": "critical|high|medium|low|none",
    "confidence": 0.0-1.0,
    "findings": [
        {{
            "type": "email|username|password|personal_info",
            "value": "found_value",
            "context": "where_found"
        }}
    ],
    "recommendations": [
        "specific action to take"
    ],
    "summary": "brief summary of findings"
}}

Focus on:
1. Exact matches of user emails/usernames
2. Partial matches that could be related
3. Associated personal information
4. Password hashes or plaintext passwords
5. Context suggesting a data breach

Be precise and only flag genuine security concerns.
"""
        return prompt

    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response into structured analysis"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                analysis = json.loads(json_str)

                # Validate required fields
                required_fields = ['risk_level', 'confidence', 'findings', 'recommendations']
                for field in required_fields:
                    if field not in analysis:
                        analysis[field] = [] if field in ['findings', 'recommendations'] else 'unknown'

                return analysis
            else:
                # Fallback parsing
                return self._fallback_parse(response)

        except json.JSONDecodeError:
            return self._fallback_parse(response)

    def _fallback_parse(self, response: str) -> Dict[str, Any]:
        """Fallback parsing when JSON extraction fails"""
        risk_keywords = {
            'critical': ['breach', 'leaked', 'compromised', 'exposed'],
            'high': ['found', 'discovered', 'potential'],
            'medium': ['suspicious', 'possible'],
            'low': ['minor', 'unlikely']
        }

        response_lower = response.lower()
        risk_level = 'none'

        for level, keywords in risk_keywords.items():
            if any(keyword in response_lower for keyword in keywords):
                risk_level = level
                break

        return {
            'risk_level': risk_level,
            'confidence': 0.5,
            'findings': [],
            'recommendations': ['Manual review recommended'],
            'summary': response[:200] + '...' if len(response) > 200 else response,
            'raw_response': response
        }

    async def generate_security_recommendations(self, account_data: Dict[str, Any]) -> List[str]:
        """Generate security recommendations for an account"""
        try:
            prompt = f"""
As a cybersecurity expert, provide specific security recommendations for this account:

Account: {account_data.get('name', 'Unknown')}
Category: {account_data.get('category', 'standard')}
Last Password Change: {account_data.get('last_password_change', 'Unknown')}
2FA Enabled: {account_data.get('has_2fa', 'Unknown')}
Recent Issues: {account_data.get('recent_issues', 'None')}

Provide 3-5 specific, actionable security recommendations in a simple list format.
Focus on immediate actions the user can take.
"""

            response = ollama.generate(
                model=self.model,
                prompt=prompt,
                options={'temperature': 0.3, 'num_predict': 300}
            )

            # Parse recommendations from response
            recommendations = []
            lines = response['response'].split('\n')

            for line in lines:
                line = line.strip()
                if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                    recommendations.append(line[1:].strip())
                elif line and len(line) > 10 and not line.endswith(':'):
                    recommendations.append(line)

            return recommendations[:5]  # Limit to 5 recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return [
                "Change password to a strong, unique password",
                "Enable two-factor authentication if available",
                "Review recent account activity",
                "Update recovery information"
            ]

    async def assess_password_strength(self, password: str) -> Dict[str, Any]:
        """Assess password strength using AI"""
        try:
            # Don't send actual password to LLM for privacy
            # Instead, analyze characteristics
            length = len(password)
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(not c.isalnum() for c in password)

            characteristics = {
                'length': length,
                'has_uppercase': has_upper,
                'has_lowercase': has_lower,
                'has_digits': has_digit,
                'has_special_chars': has_special
            }

            prompt = f"""
Assess password strength based on these characteristics:
{json.dumps(characteristics, indent=2)}

Respond with JSON:
{{
    "strength": "very_weak|weak|fair|good|strong|very_strong",
    "score": 0-100,
    "issues": ["list of issues"],
    "suggestions": ["list of improvements"]
}}
"""

            response = ollama.generate(
                model=self.model,
                prompt=prompt,
                options={'temperature': 0.1, 'num_predict': 200}
            )

            return self._parse_analysis_response(response['response'])

        except Exception as e:
            logger.error(f"Error assessing password strength: {e}")
            return {
                'strength': 'unknown',
                'score': 0,
                'issues': ['Unable to assess'],
                'suggestions': ['Use a password manager']
            }
