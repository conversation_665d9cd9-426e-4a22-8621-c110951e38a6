"""
Web Scraping utilities for PassChanger
Handles web scraping with proper rate limiting and error handling
"""

import asyncio
import logging
import random
from typing import Dict, List, Optional, Any
import aiohttp
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import validators

logger = logging.getLogger("PassChanger.WebScraper")

class WebScraper:
    def __init__(self, config: dict):
        self.config = config
        self.scraping_config = config['scraping']
        self.user_agent = UserAgent()
        self.session = None
        
    async def initialize(self):
        """Initialize web scraper"""
        try:
            # Create HTTP session with proper configuration
            connector = aiohttp.TCPConnector(
                limit=10,  # Connection pool limit
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(
                total=self.scraping_config['timeout'],
                connect=5
            )
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': self.get_random_user_agent()}
            )
            
            logger.info("Web scraper initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize web scraper: {e}")
            raise
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        try:
            return self.user_agent.random
        except Exception:
            # Fallback user agents
            fallback_agents = self.scraping_config['user_agents']
            return random.choice(fallback_agents)
    
    async def fetch_page(self, url: str, headers: Dict[str, str] = None) -> Optional[str]:
        """Fetch a web page with error handling and retries"""
        if not validators.url(url):
            logger.error(f"Invalid URL: {url}")
            return None
        
        request_headers = {'User-Agent': self.get_random_user_agent()}
        if headers:
            request_headers.update(headers)
        
        for attempt in range(self.scraping_config['max_retries']):
            try:
                # Add random delay to avoid being blocked
                if attempt > 0:
                    delay = random.uniform(1, 3)
                    await asyncio.sleep(delay)
                
                async with self.session.get(url, headers=request_headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.debug(f"Successfully fetched: {url}")
                        return content
                    
                    elif response.status == 429:
                        # Rate limited - wait longer
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited for {url}, waiting {wait_time}s")
                        await asyncio.sleep(wait_time)
                    
                    elif response.status in [403, 404]:
                        logger.warning(f"Access denied or not found: {url} (status: {response.status})")
                        return None
                    
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except asyncio.TimeoutError:
                logger.warning(f"Timeout fetching {url} (attempt {attempt + 1})")
            except Exception as e:
                logger.error(f"Error fetching {url}: {e}")
        
        logger.error(f"Failed to fetch {url} after {self.scraping_config['max_retries']} attempts")
        return None
    
    async def search_in_page(self, url: str, search_terms: List[str]) -> Dict[str, Any]:
        """Search for specific terms in a web page"""
        content = await self.fetch_page(url)
        
        if not content:
            return {'found': False, 'matches': []}
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            text_content = soup.get_text().lower()
            
            matches = []
            for term in search_terms:
                if term.lower() in text_content:
                    # Find context around the match
                    term_index = text_content.find(term.lower())
                    start = max(0, term_index - 100)
                    end = min(len(text_content), term_index + len(term) + 100)
                    context = text_content[start:end].strip()
                    
                    matches.append({
                        'term': term,
                        'context': context,
                        'position': term_index
                    })
            
            return {
                'found': len(matches) > 0,
                'matches': matches,
                'url': url,
                'title': soup.title.string if soup.title else 'No title'
            }
            
        except Exception as e:
            logger.error(f"Error parsing page {url}: {e}")
            return {'found': False, 'matches': [], 'error': str(e)}
    
    async def extract_emails_from_page(self, url: str) -> List[str]:
        """Extract email addresses from a web page"""
        content = await self.fetch_page(url)
        
        if not content:
            return []
        
        try:
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, content)
            
            # Remove duplicates and validate
            unique_emails = []
            for email in set(emails):
                if validators.email(email):
                    unique_emails.append(email)
            
            return unique_emails
            
        except Exception as e:
            logger.error(f"Error extracting emails from {url}: {e}")
            return []
    
    async def check_paste_sites(self, search_terms: List[str]) -> List[Dict[str, Any]]:
        """Check common paste sites for leaked information"""
        paste_sites = [
            'https://pastebin.com/search?q={}',
            'https://ghostbin.co/search?q={}',
            # Add more paste sites as needed
        ]
        
        results = []
        
        for term in search_terms:
            for site_template in paste_sites:
                try:
                    search_url = site_template.format(term)
                    
                    # Add delay between requests
                    await asyncio.sleep(self.scraping_config['request_delay'])
                    
                    search_result = await self.search_in_page(search_url, [term])
                    
                    if search_result['found']:
                        results.append({
                            'site': search_url,
                            'term': term,
                            'matches': search_result['matches']
                        })
                        
                except Exception as e:
                    logger.error(f"Error checking paste site for {term}: {e}")
        
        return results
    
    async def monitor_breach_forums(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """Monitor known breach forums and marketplaces"""
        # This is a placeholder for monitoring breach forums
        # In a real implementation, you would:
        # 1. Have a list of known breach forums
        # 2. Use appropriate authentication/access methods
        # 3. Search for keywords related to user accounts
        # 4. Parse results carefully
        
        logger.info("Breach forum monitoring not implemented yet")
        return []
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
            logger.info("Web scraper session closed")
