Metadata-Version: 2.4
Name: stem
Version: 1.8.2
Summary: Stem is a Python controller library that allows applications to interact with Tor (https://www.torproject.org/).
Home-page: https://stem.torproject.org/
Author: <PERSON>
Author-email: <EMAIL>
License: LGPLv3
Keywords: tor onion controller
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Topic :: Security
Classifier: Topic :: Software Development :: Libraries :: Python Modules
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: summary

For tutorials and API documentation see `<PERSON><PERSON>'s homepage <https://stem.torproject.org/>`_.

Quick Start
-----------

To install you can either use...

::

  pip install stem

... or install from the source tarball. Stem supports both the python 2.x and 3.x series. To use its python3 counterpart you simply need to install using that version of python.

::

  python3 setup.py install

After that, give some `tutorials <https://stem.torproject.org/tutorials.html>`_ a try! For questions or to discuss project ideas we're available on `irc <https://www.torproject.org/about/contact.html.en#irc>`_ and the `tor-dev@ email list <https://lists.torproject.org/cgi-bin/mailman/listinfo/tor-dev>`_.
