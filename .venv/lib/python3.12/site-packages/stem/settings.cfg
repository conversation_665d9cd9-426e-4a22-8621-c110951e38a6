################################################################################
#
# Information related to tor configuration options and events...
#
#   * manual.important   Most commonly used configuration options.
#   * manual.summary     Short summary describing the option.
#   * event.description  Descriptions for the events.
#
################################################################################

manual.important BandwidthRate
manual.important BandwidthBurst
manual.important RelayBandwidthRate
manual.important RelayBandwidthBurst
manual.important ControlPort
manual.important HashedControlPassword
manual.important CookieAuthentication
manual.important DataDirectory
manual.important Log
manual.important RunAsDaemon
manual.important User

manual.important Bridge
manual.important ExcludeNodes
manual.important MaxCircuitDirtiness
manual.important SocksPort
manual.important UseBridges

manual.important BridgeRelay
manual.important ContactInfo
manual.important ExitPolicy
manual.important MyFamily
manual.important Nickname
manual.important ORPort
manual.important AccountingMax
manual.important AccountingStart

manual.important DirPortFrontPage
manual.important DirPort

manual.important HiddenServiceDir
manual.important HiddenServicePort

# General Config Options

manual.summary.BandwidthRate Average bandwidth usage limit
manual.summary.BandwidthBurst Maximum bandwidth usage limit
manual.summary.MaxAdvertisedBandwidth Limit for the bandwidth we advertise as being available for relaying
manual.summary.RelayBandwidthRate Average bandwidth usage limit for relaying
manual.summary.RelayBandwidthBurst Maximum bandwidth usage limit for relaying
manual.summary.PerConnBWRate Average relayed bandwidth limit per connection
manual.summary.PerConnBWBurst Maximum relayed bandwidth limit per connection
manual.summary.ClientTransportPlugin Proxy when establishing bridge connections
manual.summary.ServerTransportPlugin Proxy when servicing bridge connections
manual.summary.ServerTransportListenAddr Endpoint for bridge's pluggable transport proxy
manual.summary.ServerTransportOptions Additional arguments for bridge's proxy
manual.summary.ExtORPort Endpoint for extended ORPort connections
manual.summary.ExtORPortCookieAuthFile Location of the ExtORPort's authentication cookie
manual.summary.ExtORPortCookieAuthFileGroupReadable Group read permissions for the ExtORPort's authentication cookie
manual.summary.ConnLimit Minimum number of file descriptors for Tor to start
manual.summary.DisableNetwork Don't accept non-controller connections
manual.summary.ConstrainedSockets Shrinks sockets to ConstrainedSockSize
manual.summary.ConstrainedSockSize Limit for the received and transmit buffers of sockets
manual.summary.ControlPort Port providing access to tor controllers (nyx, vidalia, etc)
manual.summary.ControlSocket Socket providing controller access
manual.summary.ControlSocketsGroupWritable Group read permissions for the control socket
manual.summary.HashedControlPassword Hash of the password for authenticating to the control port
manual.summary.CookieAuthentication If set, authenticates controllers via a cookie
manual.summary.CookieAuthFile Location of the authentication cookie
manual.summary.CookieAuthFileGroupReadable Group read permissions for the authentication cookie
manual.summary.ControlPortWriteToFile Path for a file tor writes containing its control port
manual.summary.ControlPortFileGroupReadable Group read permissions for the control port file
manual.summary.DataDirectory Location for storing runtime data (state, keys, etc)
manual.summary.DataDirectoryGroupReadable Group read permissions for the data directory
manual.summary.CacheDirectory Directory where information is cached
manual.summary.CacheDirectoryGroupReadable Group read permissions for the cache directory
manual.summary.FallbackDir Fallback when unable to retrieve descriptor information
manual.summary.UseDefaultFallbackDirs Use hard-coded fallback directory authorities when needed
manual.summary.DirAuthority Alternative directory authorities
manual.summary.DirAuthorityFallbackRate Rate at which to use fallback directory
manual.summary.AlternateDirAuthority Alternative directory authorities (consensus only)
manual.summary.AlternateBridgeAuthority Alternative directory authorities (bridges only)
manual.summary.DisableAllSwap Locks all allocated memory so they can't be paged out
manual.summary.DisableDebuggerAttachment Limit information applications can retrieve about the process
manual.summary.FetchDirInfoEarly Keeps consensus information up to date, even if unnecessary
manual.summary.FetchDirInfoExtraEarly Updates consensus information when it's first available
manual.summary.FetchHidServDescriptors Toggles if hidden service descriptors are fetched automatically or not
manual.summary.FetchServerDescriptors Toggles if the consensus is fetched automatically or not
manual.summary.FetchUselessDescriptors Toggles if relay descriptors are fetched when they aren't strictly necessary
manual.summary.HTTPProxy HTTP proxy for connecting to tor
manual.summary.HTTPProxyAuthenticator Authentication credentials for HTTPProxy
manual.summary.HTTPSProxy SSL proxy for connecting to tor
manual.summary.HTTPSProxyAuthenticator Authentication credentials for HTTPSProxy
manual.summary.Sandbox Run within a syscall sandbox
manual.summary.Socks4Proxy SOCKS 4 proxy for connecting to tor
manual.summary.Socks5Proxy SOCKS 5 for connecting to tor
manual.summary.Socks5ProxyUsername Username for connecting to the Socks5Proxy
manual.summary.Socks5ProxyPassword Password for connecting to the Socks5Proxy
manual.summary.UnixSocksGroupWritable Group write permissions for the socks socket
manual.summary.KeepalivePeriod Rate at which to send keepalive packets
manual.summary.Log Runlevels and location for tor logging
manual.summary.LogMessageDomains Includes a domain when logging messages
manual.summary.MaxUnparseableDescSizeToLog Size of the dedicated log for unparseable descriptors
manual.summary.OutboundBindAddress Sets the IP used for connecting to tor
manual.summary.OutboundBindAddressOR Make outbound non-exit connections originate from this address
manual.summary.OutboundBindAddressExit Make outbound exit connections originate from this address
manual.summary.PidFile Path for a file tor writes containing its process id
manual.summary.ProtocolWarnings Toggles if protocol errors give warnings or not
manual.summary.RunAsDaemon Toggles if tor runs as a daemon process
manual.summary.LogTimeGranularity limits granularity of log message timestamps
manual.summary.TruncateLogFile Overwrites log file rather than appending when restarted
manual.summary.SyslogIdentityTag Tag logs appended to the syslog as being from tor
manual.summary.AndroidIdentityTag Tag when logging to android subsystem
manual.summary.SafeLogging Toggles if logs are scrubbed of sensitive information
manual.summary.User UID for the process when started
manual.summary.KeepBindCapabilities Retain permission for binding to low valued ports
manual.summary.HardwareAccel Toggles if tor attempts to use hardware acceleration
manual.summary.AccelName OpenSSL engine name for crypto acceleration
manual.summary.AccelDir Crypto acceleration library path
manual.summary.AvoidDiskWrites Toggles if tor avoids frequently writing to disk
manual.summary.CircuitPriorityHalflife Overwrite method for prioritizing traffic among relayed connections
manual.summary.CountPrivateBandwidth Applies rate limiting to private IP addresses
manual.summary.ExtendByEd25519ID Include Ed25519 identifier when extending circuits
manual.summary.NoExec Prevents any launch of other executables
manual.summary.Schedulers Scheduling algorithm by which to send outbound data
manual.summary.KISTSchedRunInterval Scheduling interval if using KIST
manual.summary.KISTSockBufSizeFactor Multiplier for per-socket limit if using KIST

# Client Config Options

manual.summary.Bridge Available bridges
manual.summary.LearnCircuitBuildTimeout Toggles adaptive timeouts for circuit creation
manual.summary.CircuitBuildTimeout Initial timeout for circuit creation
manual.summary.CircuitsAvailableTimeout Time to keep circuits open and unused for
manual.summary.CircuitStreamTimeout Timeout for shifting streams among circuits
manual.summary.ClientOnly Ensures that we aren't used as a relay or directory mirror
manual.summary.ConnectionPadding Pad traffic to help prevent correlation attacks
manual.summary.ReducedConnectionPadding Reduce padding and increase circuit cycling for low bandidth connections
manual.summary.CircuitPadding Pad circuit traffic to help prevent correlation attacks
manual.summary.ReducedCircuitPadding Only use lightweight padding algorithms
manual.summary.ExcludeNodes Relays or locales never to be used in circuits
manual.summary.ExcludeExitNodes Relays or locales never to be used for exits
manual.summary.GeoIPExcludeUnknown Don't use relays with an unknown locale in circuits
manual.summary.ExitNodes Preferred final hop for circuits
manual.summary.MiddleNodes Preferred middle hops for circuits
manual.summary.EntryNodes Preferred first hops for circuits
manual.summary.StrictNodes Never uses notes outside of Entry/ExitNodes
manual.summary.FascistFirewall Only make outbound connections on FirewallPorts
manual.summary.FirewallPorts Ports used by FascistFirewall
manual.summary.HidServAuth Authentication credentials for connecting to a hidden service
manual.summary.ClientOnionAuthDir Path containing hidden service authorization files
manual.summary.ReachableAddresses Rules for bypassing the local firewall
manual.summary.ReachableDirAddresses Rules for bypassing the local firewall (directory fetches)
manual.summary.ReachableORAddresses Rules for bypassing the local firewall (OR connections)
manual.summary.LongLivedPorts Ports requiring highly reliable relays
manual.summary.MapAddress Alias mappings for address requests
manual.summary.NewCircuitPeriod Period for considering the creation of new circuits
manual.summary.MaxCircuitDirtiness Duration for reusing constructed circuits
manual.summary.MaxClientCircuitsPending Number of circuits that can be in construction at once
manual.summary.NodeFamily Define relays as belonging to a family
manual.summary.EnforceDistinctSubnets Prevent use of multiple relays from the same subnet on a circuit
manual.summary.SocksPort Port for using tor as a Socks proxy
manual.summary.SocksPolicy Access policy for the pocks port
manual.summary.SocksTimeout Time until idle or unestablished socks connections are closed
manual.summary.TokenBucketRefillInterval Frequency at which exhausted connections are checked for new traffic
manual.summary.TrackHostExits Maintains use of the same exit whenever connecting to this destination
manual.summary.TrackHostExitsExpire Time until use of an exit for tracking expires
manual.summary.UpdateBridgesFromAuthority Toggles fetching bridge descriptors from the authorities
manual.summary.UseBridges Make use of configured bridges
manual.summary.UseEntryGuards Use guard relays for first hop
manual.summary.GuardfractionFile File containing information with duration of our guards
manual.summary.UseGuardFraction Take guardfraction into account for path selection
manual.summary.NumEntryGuards Pool size of guard relays we'll select from
manual.summary.NumPrimaryGuards Pool size of strongly preferred guard relays we'll select from
manual.summary.NumDirectoryGuards Pool size of directory guards we'll select from
manual.summary.GuardLifetime Minimum time to keep entry guards
manual.summary.SafeSocks Toggles rejecting unsafe variants of the socks protocol
manual.summary.TestSocks Provide notices for if socks connections are of the safe or unsafe variants
manual.summary.VirtualAddrNetworkIPv4 IPv4 address range to use when needing a virtual address
manual.summary.VirtualAddrNetworkIPv6 IPv6 address range to use when needing a virtual address
manual.summary.AllowNonRFC953Hostnames Toggles blocking invalid characters in hostname resolution
manual.summary.HTTPTunnelPort Port on which to allow 'HTTP CONNECT' connections
manual.summary.TransPort Port for transparent proxying if the OS supports it
manual.summary.TransProxyType Proxy type to be used
manual.summary.NATDPort Port for forwarding ipfw NATD connections
manual.summary.AutomapHostsOnResolve Map addresses ending with special suffixes to virtual addresses
manual.summary.AutomapHostsSuffixes Address suffixes recognized by AutomapHostsOnResolve
manual.summary.DNSPort Port from which DNS responses are fetched instead of tor
manual.summary.ClientDNSRejectInternalAddresses Disregards anonymous DNS responses for internal addresses
manual.summary.ClientRejectInternalAddresses Disables use of Tor for internal connections
manual.summary.DownloadExtraInfo Toggles fetching of extra information about relays
manual.summary.WarnPlaintextPorts Toggles warnings for using risky ports
manual.summary.RejectPlaintextPorts Prevents connections on risky ports
manual.summary.OptimisticData Use exits without confirmation that prior connections succeeded
manual.summary.HSLayer2Nodes permissible relays for the second hop of HS circuits
manual.summary.HSLayer3Nodes permissible relays for the third hop of HS circuits
manual.summary.UseMicrodescriptors Retrieve microdescriptors rather than server descriptors
manual.summary.PathBiasCircThreshold Number of circuits through a guard before applying bias checks
manual.summary.PathBiasNoticeRate Fraction of circuits that must succeed before logging a notice
manual.summary.PathBiasWarnRate Fraction of circuits that must succeed before logging a warning
manual.summary.PathBiasExtremeRate Fraction of circuits that must succeed before logging an error
manual.summary.PathBiasDropGuards Drop guards failing to establish circuits
manual.summary.PathBiasScaleThreshold Circuits through a guard before scaling past observations down
manual.summary.PathBiasUseThreshold Number of streams through a circuit before applying bias checks
manual.summary.PathBiasNoticeUseRate Fraction of streams that must succeed before logging a notice
manual.summary.PathBiasExtremeUseRate Fraction of streams that must succeed before logging an error
manual.summary.PathBiasScaleUseThreshold Streams through a circuit before scaling past observations down
manual.summary.ClientUseIPv4 Allow IPv4 connections to guards and fetching consensus
manual.summary.ClientUseIPv6 Allow IPv6 connections to guards and fetching consensus
manual.summary.ClientPreferIPv6DirPort Perfer relays with IPv6 when fetching consensus
manual.summary.ClientPreferIPv6ORPort Prefer a guard's IPv6 rather than IPv4 endpoint
manual.summary.ClientAutoIPv6ORPort Connect to IPv6 ORPorts when available
manual.summary.PathsNeededToBuildCircuits Portion of relays to require information for before making circuits
manual.summary.ClientBootstrapConsensusAuthorityDownloadInitialDelay Delay when bootstrapping before downloading descriptors from authorities
manual.summary.ClientBootstrapConsensusFallbackDownloadInitialDelay Delay when bootstrapping before downloading descriptors from fallbacks
manual.summary.ClientBootstrapConsensusAuthorityOnlyDownloadInitialDelay Delay when bootstrapping before downloading descriptors from authorities if fallbacks disabled
manual.summary.ClientBootstrapConsensusMaxInProgressTries Descriptor documents that can be downloaded in parallel
manual.summary.DormantClientTimeout Become dormant when unused as a client for this duration
manual.summary.DormantTimeoutDisabledByIdleStreams Include idle streams when determining dormancy
manual.summary.DormantOnFirstStartup Begin tor in dormant mode
manual.summary.DormantCanceledByStartup Disable dormant mode when tor's restarted

# Server Config Options

manual.summary.Address Overwrites address others will use to reach this relay
manual.summary.AssumeReachable Skips reachability test at startup
manual.summary.BridgeRelay Act as a bridge
manual.summary.BridgeDistribution Distribution method BrideDB should provide our address by
manual.summary.ContactInfo Contact information for this relay
manual.summary.ExitRelay Allow relaying of exit traffic
manual.summary.ExitPolicy Traffic destinations that can exit from this relay
manual.summary.ExitPolicyRejectPrivate Prevent exiting on the local network
manual.summary.ExitPolicyRejectLocalInterfaces More extensive prevention of exiting on the local network
manual.summary.ReducedExitPolicy Customized reduced exit policy 
manual.summary.IPv6Exit Allow clients to use us for IPv6 traffic
manual.summary.MaxOnionQueueDelay Duration to reject new onionskins if we have more than we can process
manual.summary.MyFamily Other relays this operator administers
manual.summary.Nickname Identifier for this relay
manual.summary.NumCPUs Number of processes spawned for decryption
manual.summary.ORPort Port used to accept relay traffic
manual.summary.PublishServerDescriptor Types of descriptors published
manual.summary.ShutdownWaitLength Delay before quitting after receiving a SIGINT signal
manual.summary.SSLKeyLifetime Lifetime for our link certificate
manual.summary.HeartbeatPeriod Rate at which an INFO level heartbeat message is sent
manual.summary.MainloopStats Include development information from the main loop with heartbeats
manual.summary.AccountingMax Amount of traffic before hibernating
manual.summary.AccountingRule Method to determine when the accounting limit is reached
manual.summary.AccountingStart Duration of an accounting period
manual.summary.RefuseUnknownExits Prevents relays not in the consensus from using us as an exit
manual.summary.ServerDNSResolvConfFile Overriding resolver config for DNS queries we provide
manual.summary.ServerDNSAllowBrokenConfig Toggles if we persist despite configuration parsing errors or not
manual.summary.ServerDNSSearchDomains Toggles if our DNS queries search for addresses in the local domain
manual.summary.ServerDNSDetectHijacking Toggles testing for DNS hijacking
manual.summary.ServerDNSTestAddresses Addresses to test to see if valid DNS queries are being hijacked
manual.summary.ServerDNSAllowNonRFC953Hostnames Toggles if we reject DNS queries with invalid characters
manual.summary.BridgeRecordUsageByCountry Tracks geoip information on bridge usage
manual.summary.ServerDNSRandomizeCase Toggles DNS query case randomization
manual.summary.GeoIPFile Path to file containing IPv4 geoip information
manual.summary.GeoIPv6File Path to file containing IPv6 geoip information
manual.summary.CellStatistics Toggles storing circuit queue duration to disk
manual.summary.PaddingStatistics Toggles storing padding counts
manual.summary.DirReqStatistics Toggles storing network status counts and performance to disk
manual.summary.EntryStatistics Toggles storing client connection counts to disk
manual.summary.ExitPortStatistics Toggles storing traffic and port usage data to disk
manual.summary.ConnDirectionStatistics Toggles storing connection use to disk
manual.summary.HiddenServiceStatistics Toggles storing hidden service stats to disk
manual.summary.ExtraInfoStatistics Publishes statistic data in the extra-info documents
manual.summary.ExtendAllowPrivateAddresses Allow circuits to be extended to the local network
manual.summary.MaxMemInQueues Threshold at which tor will terminate circuits to avoid running out of memory
manual.summary.DisableOOSCheck Don't close connections when running out of sockets
manual.summary.SigningKeyLifetime Duration the Ed25519 signing key is valid for
manual.summary.OfflineMasterKey Don't generate the master secret key
manual.summary.KeyDirectory Directory where secret keys reside
manual.summary.KeyDirectoryGroupReadable Group read permissions for the secret key directory

# Directory Server Options

manual.summary.DirPortFrontPage Publish this html file on the DirPort
manual.summary.DirPort Port for directory connections
manual.summary.DirPolicy Access policy for the DirPort
manual.summary.DirCache Provide cached descriptor information to other tor users
manual.summary.MaxConsensusAgeForDiffs Time to generate consensus caches for

# Directory Authority Server Options

manual.summary.AuthoritativeDirectory Act as a directory authority
manual.summary.V3AuthoritativeDirectory Generates a version 3 consensus
manual.summary.VersioningAuthoritativeDirectory Provides opinions on recommended versions of tor
manual.summary.RecommendedVersions Suggested versions of tor
manual.summary.RecommendedClientVersions Tor versions believed to be safe for clients
manual.summary.BridgeAuthoritativeDir Acts as a bridge authority
manual.summary.MinUptimeHidServDirectoryV2 Required uptime before accepting hidden service directory
manual.summary.RecommendedServerVersions Tor versions believed to be safe for relays
manual.summary.ConsensusParams Params entry of the networkstatus vote
manual.summary.DirAllowPrivateAddresses Toggles allowing arbitrary input or non-public IPs in descriptors
manual.summary.AuthDirBadExit Relays to be flagged as bad exits
manual.summary.AuthDirInvalid Relays from which the valid flag is withheld
manual.summary.AuthDirReject Relays to be dropped from the consensus
manual.summary.AuthDirBadExitCCs Countries for which to flag all relays as bad exits
manual.summary.AuthDirInvalidCCs Countries for which the valid flag is withheld
manual.summary.AuthDirRejectCCs Countries for which relays aren't accepted into the consensus
manual.summary.AuthDirListBadExits Toggles if we provide an opinion on bad exits
manual.summary.AuthDirMaxServersPerAddr Limit on the number of relays accepted per ip
manual.summary.AuthDirFastGuarantee Advertised rate at which the Fast flag is granted
manual.summary.AuthDirGuardBWGuarantee Advertised rate necessary to be a guard
manual.summary.AuthDirPinKeys Don't accept descriptors with conflicting identity keypairs
manual.summary.AuthDirSharedRandomness Participates in shared randomness voting
manual.summary.AuthDirTestEd25519LinkKeys Require proper Ed25519 key for the Running flag
manual.summary.BridgePassword Password for requesting bridge information
manual.summary.V3AuthVotingInterval Consensus voting interval
manual.summary.V3AuthVoteDelay Wait time to collect votes of other authorities
manual.summary.V3AuthDistDelay Wait time to collect the signatures of other authorities
manual.summary.V3AuthNIntervalsValid Number of voting intervals a consensus is valid for
manual.summary.V3BandwidthsFile Path to a file containing measured relay bandwidths
manual.summary.V3AuthUseLegacyKey Signs consensus with both the current and legacy keys
manual.summary.RephistTrackTime Discards old, unchanged reliability information
manual.summary.AuthDirHasIPv6Connectivity Descriptors can be retrieved over the authority's IPv6 ORPort
manual.summary.MinMeasuredBWsForAuthToIgnoreAdvertised Total measured value before advertised bandwidths are treated as unreliable

# Hidden Service Options

manual.summary.HiddenServiceDir Directory contents for the hidden service
manual.summary.HiddenServicePort Port the hidden service is provided on
manual.summary.PublishHidServDescriptors Toggles automated publishing of the hidden service to the rendezvous directory
manual.summary.HiddenServiceVersion Version for published hidden service descriptors
manual.summary.HiddenServiceAuthorizeClient Restricts access to the hidden service
manual.summary.HiddenServiceAllowUnknownPorts Allow rendezvous circuits on unrecognized ports
manual.summary.HiddenServiceExportCircuitID Exposes incoming client circuits via the given protocol
manual.summary.HiddenServiceMaxStreams Maximum streams per rendezvous circuit
manual.summary.HiddenServiceMaxStreamsCloseCircuit Closes rendezvous circuits that exceed the maximum number of streams
manual.summary.RendPostPeriod Period at which the rendezvous service descriptors are refreshed
manual.summary.HiddenServiceDirGroupReadable Group read permissions for the hidden service directory
manual.summary.HiddenServiceNumIntroductionPoints Number of introduction points the hidden service will have
manual.summary.HiddenServiceEnableIntroDoSDefense Introduction point DoS protection
manual.summary.HiddenServiceEnableIntroDoSRatePerSec Request rate allowed for the introduction point
manual.summary.HiddenServiceEnableIntroDoSBurstPerSec Burst rate allowed for the introduction point
manual.summary.HiddenServiceSingleHopMode Allow non-anonymous single hop hidden services
manual.summary.HiddenServiceNonAnonymousMode Enables HiddenServiceSingleHopMode to be set

# DoS Mitigation Options

manual.summary.DoSCircuitCreationEnabled Enables circuit creation DoS mitigation
manual.summary.DoSCircuitCreationMinConnections Connection rate when clients are a suspected DoS
manual.summary.DoSCircuitCreationRate Acceptable rate for circuit creation
manual.summary.DoSCircuitCreationBurst Accept burst of circuit creation up to this rate
manual.summary.DoSCircuitCreationDefenseType Method for mitigating circuit creation DoS
manual.summary.DoSCircuitCreationDefenseTimePeriod Duration of DoS mitigation
manual.summary.DoSConnectionEnabled Enables connection DoS mitigation
manual.summary.DoSConnectionMaxConcurrentCount Acceptable number of connections
manual.summary.DoSConnectionDefenseType Method for mitigating connection DoS
manual.summary.DoSRefuseSingleHopClientRendezvous Prevent establishment of single hop rendezvous points

# Testing Network Options

manual.summary.TestingTorNetwork Overrides other options to be a testing network
manual.summary.TestingV3AuthInitialVotingInterval Overrides V3AuthVotingInterval for the first consensus
manual.summary.TestingV3AuthInitialVoteDelay Overrides TestingV3AuthInitialVoteDelay for the first consensus
manual.summary.TestingV3AuthInitialDistDelay Overrides TestingV3AuthInitialDistDelay for the first consensus
manual.summary.TestingV3AuthVotingStartOffset Offset for the point at which the authority votes
manual.summary.TestingAuthDirTimeToLearnReachability Delay until opinions are given about which relays are running or not
manual.summary.TestingEstimatedDescriptorPropagationTime Delay before clients attempt to fetch descriptors from directory caches
manual.summary.TestingMinFastFlagThreshold Minimum value for the Fast flag
manual.summary.TestingServerDownloadInitialDelay Delay before downloading resources for relaying
manual.summary.TestingClientDownloadInitialDelay Delay before downloading resources for client usage
manual.summary.TestingServerConsensusDownloadInitialDelay Delay before downloading descriptors for relaying
manual.summary.TestingClientConsensusDownloadInitialDelay Delay before downloading descriptors for client usage
manual.summary.TestingBridgeDownloadInitialDelay Delay before downloading bridge descriptors
manual.summary.TestingBridgeBootstrapDownloadInitialDelay Delay before downloading bridge descriptors when first started
manual.summary.TestingClientMaxIntervalWithoutRequest Maximum time to wait to batch requests for missing descriptors
manual.summary.TestingDirConnectionMaxStall Duration to let directory connections stall before timing out
manual.summary.TestingDirAuthVoteExit Relays to give the Exit flag to
manual.summary.TestingDirAuthVoteExitIsStrict Only grant the Exit flag to relays listed by TestingDirAuthVoteExit
manual.summary.TestingDirAuthVoteGuard Relays to give the Guard flag to
manual.summary.TestingDirAuthVoteGuardIsStrict Only grant the Guard flag to relays listed by TestingDirAuthVoteGuard
manual.summary.TestingDirAuthVoteHSDir Relays to give the HSDir flag to
manual.summary.TestingDirAuthVoteHSDirIsStrict Only grant the HSDir flag to relays listed by TestingDirAuthVoteHSDir
manual.summary.TestingEnableConnBwEvent Allow controllers to request CONN_BW events
manual.summary.TestingEnableCellStatsEvent Allow controllers to request CELL_STATS events
manual.summary.TestingMinExitFlagThreshold Lower bound for assigning the Exit flag
manual.summary.TestingLinkCertLifetime Duration of our ed25519 certificate
manual.summary.TestingAuthKeyLifetime Duration for our ed25519 signing key
manual.summary.TestingLinkKeySlop Time before expiration that we replace our ed25519 link key
manual.summary.TestingAuthKeySlop Time before expiration that we replace our ed25519 authentication key
manual.summary.TestingSigningKeySlop Time before expiration that we replace our ed25519 signing key

# Brief description of tor events

event.description.debug Logging at the debug runlevel. This is low level, high volume information about tor's internals that generally isn't useful to users.
event.description.info Logging at the info runlevel. This is low level information of important internal processes.
event.description.notice Logging at the notice runlevel. This runlevel and above are shown to users by default, and includes general information the user should be aware of.
event.description.warn Logging at the warning runlevel. These are problems the user should be aware of.
event.description.err Logging at the error runlevel. These are critical issues that may prevent tor from working properly.

event.description.addrmap New address mapping for our DNS cache.
event.description.authdir_newdescs Indicates we just received a new descriptor. This is only used by directory authorities.
event.description.buildtimeout_set Indicates the timeout value for a circuit has changed.
event.description.bw Event emitted every second with the bytes sent and received by tor.
event.description.cell_stats Event emitted every second with the count of the number of cell types per circuit.
event.description.circ Indicates that a circuit we've established through the tor network has been created, changed, or closed.
event.description.circ_bw Event emitted every second with the bytes sent and received on a per-circuit basis.
event.description.circ_minor Minor changes to our circuits, such as reuse of existing circuits for a different purpose.
event.description.clients_seen Periodic summary of the countries we've seen users connect from. This is only used by bridge relays.
event.description.conf_changed Indicates that our torrc configuration has changed. This could be in response to a SETCONF or RELOAD signal.
event.description.conn_bw Event emitted every second with the byytes sent and received on a per-connection basis.
event.description.descchanged Indicates that our descriptor has changed.
event.description.guard Indicates that the set of relays we use for our initial connection into the tor network (guards) have changed.
event.description.hs_desc Received a hidden service descriptor that wasn't yet cached.
event.description.hs_desc_content Content of a hidden service descriptor we've fetched.
event.description.network_liveness Emitted when the network becomes reachable or unreachable.
event.description.newconsensus Received a new hourly consensus of relays in the tor network.
event.description.newdesc Indicates that a new descriptor is available.
event.description.ns Consensus information for an individual relay has changed. This could be due to receiving a new consensus or tor locally decides a relay is up or down.
event.description.orconn Change in our connections as a relay.
event.description.signal Indicates that tor has received and acted upon a signal being sent to its process.
event.description.status_client Notification of a change in tor's state as a client (ie user).
event.description.status_general Notification of a change in tor's state.
event.description.status_server Notification of a change in tor's state as a relay.
event.description.stream Communication over a circuit we've established. For instance, Firefox making a connection through tor.
event.description.stream_bw Event emitted every second with the bytes sent and received for a specific stream.
event.description.tb_empty Statistics for when token buckets are refilled. This is only used when TestingTorNetwork is set.
event.description.transport_launched Emitted when a pluggable transport is launched.

