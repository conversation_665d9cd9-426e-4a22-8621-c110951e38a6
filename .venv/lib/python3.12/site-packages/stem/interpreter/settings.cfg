################################################################################
#
# Configuration data used by <PERSON><PERSON>'s interpreter prompt.
#
################################################################################

 ##################
# GENERAL MESSAGES #
 ##################

msg.multiline_unimplemented_notice Multi-line control options like this are not yet implemented.

msg.help
|Interactive interpreter for Tor. This provides you with direct access
|to Tor's control interface via either python or direct requests.
|
|  -i, --interface [ADDRESS:]PORT  change control interface from {address}:{port}
|  -s, --socket SOCKET_PATH        attach using unix domain socket if present,
|                                    SOCKET_PATH defaults to: {socket}
|      --tor PATH                  tor binary if tor isn't already running
|      --run                       executes the given command or file of commands
|  --no-color                      disables colorized output
|  -h, --help                      presents this help
|

msg.startup_banner
|Welcome to Stem's interpreter prompt. This provides you with direct access to
|Tor's control interface.
|
|This acts like a standard python interpreter with a Tor connection available
|via your 'controller' variable...
|
|  >>> controller.get_info('version')
|  '*******-alpha-dev (git-245ecfff36c0cecc)'
|
|You can also issue requests directly to Tor...
|
|  >>> GETINFO version
|  250-version=*******-alpha-dev (git-245ecfff36c0cecc)
|  250 OK
|
|For more information run '/help'.
|

msg.tor_unavailable Tor isn't running and the command currently isn't in your PATH.
msg.unable_to_start_tor Unable to start tor: {error}
msg.unable_to_read_file Unable to read {path}: {error}

msg.starting_tor
|Tor isn't running. Starting a temporary Tor instance for our interpreter to
|interact with. This will have a minimal non-relaying configuration, and be
|shut down when you're done.
|
|--------------------------------------------------------------------------------
|

 #################
# OUTPUT OF /HELP #
 #################

# Response for the '/help' command without any arguments.

help.general
|Interpreter commands include:
|  /help   - provides information for interpreter and tor commands
|  /events - prints events that we've received
|  /info   - general information for a relay
|  /python - enable or disable support for running python commands
|  /quit   - shuts down the interpreter
|
|Tor commands include:
|  GETINFO - queries information from tor
|  GETCONF, SETCONF, RESETCONF - show or edit a configuration option
|  SIGNAL - issues control signal to the process (for resetting, stopping, etc)
|  SETEVENTS - configures the events tor will notify us of
|
|  USEFEATURE - enables custom behavior for the controller
|  SAVECONF - writes tor's current configuration to our torrc
|  LOADCONF - loads the given input like it was part of our torrc
|  MAPADDRESS - replaces requests for one address with another
|  POSTDESCRIPTOR - adds a relay descriptor to our cache
|  EXTENDCIRCUIT - create or extend a tor circuit
|  SETCIRCUITPURPOSE - configures the purpose associated with a circuit
|  CLOSECIRCUIT - closes the given circuit
|  ATTACHSTREAM - associates an application's stream with a tor circuit
|  REDIRECTSTREAM - sets a stream's destination
|  CLOSESTREAM - closes the given stream
|  ADD_ONION - create a new hidden service
|  DEL_ONION - delete a hidden service that was created with ADD_ONION
|  HSFETCH - retrieve a hidden service descriptor, providing it in a HS_DESC_CONTENT event
|  HSPOST - uploads a hidden service descriptor
|  RESOLVE - issues an asynchronous dns or rdns request over tor
|  TAKEOWNERSHIP - instructs tor to quit when this control connection is closed
|  PROTOCOLINFO - queries version and controller authentication information
|  QUIT - disconnect the control connection
|
|For more information use '/help [OPTION]'.

# Usage of tor and interpreter commands.

help.usage HELP => /help [OPTION]
help.usage EVENTS => /events [types]
help.usage INFO => /info [relay fingerprint, nickname, or IP address]
help.usage PYTHON => /python [enable,disable]
help.usage QUIT => /quit
help.usage GETINFO => GETINFO OPTION
help.usage GETCONF => GETCONF OPTION
help.usage SETCONF => SETCONF PARAM[=VALUE]
help.usage RESETCONF => RESETCONF PARAM[=VALUE]
help.usage SIGNAL => SIGNAL SIG
help.usage SETEVENTS => SETEVENTS [EXTENDED] [EVENTS]
help.usage USEFEATURE => USEFEATURE OPTION
help.usage SAVECONF => SAVECONF
help.usage LOADCONF => LOADCONF...
help.usage MAPADDRESS => MAPADDRESS SOURCE_ADDR=DESTINATION_ADDR
help.usage POSTDESCRIPTOR => POSTDESCRIPTOR [purpose=general/controller/bridge] [cache=yes/no]...
help.usage EXTENDCIRCUIT => EXTENDCIRCUIT CircuitID [PATH] [purpose=general/controller]
help.usage SETCIRCUITPURPOSE => SETCIRCUITPURPOSE CircuitID purpose=general/controller
help.usage CLOSECIRCUIT => CLOSECIRCUIT CircuitID [IfUnused]
help.usage ATTACHSTREAM => ATTACHSTREAM StreamID CircuitID [HOP=HopNum]
help.usage REDIRECTSTREAM => REDIRECTSTREAM StreamID Address [Port]
help.usage CLOSESTREAM => CLOSESTREAM StreamID Reason [Flag]
help.usage ADD_ONION => KeyType:KeyBlob [Flags=Flag] (Port=Port [,Target])...
help.usage DEL_ONION => ServiceID
help.usage HSFETCH => HSFETCH (HSAddress/v2-DescId) [SERVER=Server]...
help.usage HSPOST => [SERVER=Server] DESCRIPTOR
help.usage RESOLVE => RESOLVE [mode=reverse] address
help.usage TAKEOWNERSHIP => TAKEOWNERSHIP
help.usage PROTOCOLINFO => PROTOCOLINFO [ProtocolVersion]

# Longer description of what tor and interpreter commands do.

help.description.help
|Provides usage information for the given interpreter, tor command, or tor
|configuration option.
|
|Example:
|  /help info        # provides a description of the '/info' option
|  /help GETINFO     # usage information for tor's GETINFO controller option

help.description.events
|Provides events that we've received belonging to the given event types. If
|no types are specified then this provides all the messages that we've
|received.
|
|You can also run '/events clear' to clear the backlog of events we've
|received.

help.description.info
|Provides information for a relay that's currently in the consensus. If no
|relay is specified then this provides information on ourselves.

help.description.python
|Enables or disables support for running python commands. This determines how
|we treat commands this interpreter doesn't recognize...
|
|* If enabled then unrecognized commands are executed as python.
|* If disabled then unrecognized commands are passed along to tor.

help.description.quit
|Terminates the interpreter.

help.description.getinfo
|Queries the tor process for information. Options are...
|

help.description.getconf
|Provides the current value for a given configuration value. Options include...
|

help.description.setconf
|Sets the given configuration parameters. Values can be quoted or non-quoted
|strings, and reverts the option to 0 or NULL if not provided.
|
|Examples:
|  * Sets a contact address and resets our family to NULL
|    SETCONF MyFamily ContactInfo=<EMAIL>
|
|  * Sets an exit policy that only includes port 80/443
|    SETCONF ExitPolicy=\"accept *:80, accept *:443, reject *:*\"\

help.description.resetconf
|Reverts the given configuration options to their default values. If a value
|is provided then this behaves in the same way as SETCONF.
|
|Examples:
|  * Returns both of our accounting parameters to their defaults
|    RESETCONF AccountingMax AccountingStart
|
|  * Uses the default exit policy and sets our nickname to be 'Goomba'
|    RESETCONF ExitPolicy Nickname=Goomba

help.description.signal
|Issues a signal that tells the tor process to reload its torrc, dump its
|stats, halt, etc.

help.description.setevents
|Sets the events that we will receive. This turns off any events that aren't
|listed so sending 'SETEVENTS' without any values will turn off all event reporting.
|
|For Tor versions between ******* and ******* adding 'EXTENDED' causes some
|events to give us additional information. After version ******* this is
|always on.
|
|Events include...
|

help.description.usefeature
|Customizes the behavior of the control port. Options include...
|

help.description.saveconf
|Writes Tor's current configuration to its torrc.

help.description.loadconf
|Reads the given text like it belonged to our torrc.
|
|Example:
|  +LOADCONF
|  # sets our exit policy to just accept ports 80 and 443
|  ExitPolicy accept *:80
|  ExitPolicy accept *:443
|  ExitPolicy reject *:*
|  .

help.description.mapaddress
|Replaces future requests for one address with another.
|
|Example:
|  MAPADDRESS 0.0.0.0=torproject.org *******=tor.freehaven.net

help.description.postdescriptor
|Simulates getting a new relay descriptor.

help.description.extendcircuit
|Extends the given circuit or create a new one if the CircuitID is zero. The
|PATH is a comma separated list of fingerprints. If it isn't set then this
|uses Tor's normal path selection.

help.description.setcircuitpurpose
|Sets the purpose attribute for a circuit.

help.description.closecircuit
|Closes the given circuit. If "IfUnused" is included then this only closes
|the circuit if it isn't currently being used.

help.description.attachstream
|Attaches a stream with the given built circuit (tor picks one on its own if
|CircuitID is zero). If HopNum is given then this hop is used to exit the
|circuit, otherwise the last relay is used.

help.description.redirectstream
|Sets the destination for a given stream. This can only be done after a
|stream is created but before it's attached to a circuit.

help.description.closestream
|Closes the given stream, the reason being an integer matching a reason as
|per section 6.3 of the tor-spec.

help.description.add_onion
|Creates a new hidden service. Unlike 'SETCONF HiddenServiceDir...' this
|doesn't persist the service to disk.

help.description.del_onion
|Delete a hidden service that was created with ADD_ONION.

help.description.hsfetch
|Retrieves the descriptor for a hidden service. This is an asynchronous
|request, with the descriptor provided by a HS_DESC_CONTENT event.

help.description.hspost
|Uploads a descriptor to a hidden service directory.

help.description.resolve
|Performs IPv4 DNS resolution over tor, doing a reverse lookup instead if
|"mode=reverse" is included. This request is processed in the background and
|results in a ADDRMAP event with the response.

help.description.takeownership
|Instructs Tor to gracefully shut down when this control connection is closed.

help.description.protocolinfo
|Provides bootstrapping information that a controller might need when first
|starting, like Tor's version and controller authentication. This can be done
|before authenticating to the control port.

help.signal.options RELOAD / HUP => reload our torrc
help.signal.options SHUTDOWN / INT => gracefully shut down, waiting 30 seconds if we're a relay
help.signal.options DUMP / USR1 => logs information about open connections and circuits
help.signal.options DEBUG / USR2 => makes us log at the DEBUG runlevel
help.signal.options HALT / TERM => immediately shut down
help.signal.options CLEARDNSCACHE => clears any cached DNS results
help.signal.options NEWNYM => clears the DNS cache and uses new circuits for future connections

 ################
# TAB COMPLETION #
 ################

# Commands we'll autocomplete when the user hits tab. This is just the start of
# our autocompletion list - more are determined dynamically by checking what
# tor supports.

autocomplete /help
autocomplete /events
autocomplete /info
autocomplete /quit
autocomplete SAVECONF
autocomplete MAPADDRESS
autocomplete EXTENDCIRCUIT
autocomplete SETCIRCUITPURPOSE
autocomplete SETROUTERPURPOSE
autocomplete ATTACHSTREAM
#autocomplete +POSTDESCRIPTOR  # TODO: needs multi-line support
autocomplete REDIRECTSTREAM
autocomplete CLOSESTREAM
autocomplete CLOSECIRCUIT
autocomplete QUIT
autocomplete RESOLVE
autocomplete PROTOCOLINFO
#autocomplete +LOADCONF  # TODO: needs multi-line support
autocomplete TAKEOWNERSHIP
autocomplete AUTHCHALLENGE
autocomplete DROPGUARDS
autocomplete ADD_ONION NEW:BEST
autocomplete ADD_ONION NEW:RSA1024
autocomplete ADD_ONION NEW:ED25519-V3
autocomplete ADD_ONION RSA1024:
autocomplete ADD_ONION ED25519-V3:
autocomplete DEL_ONION
autocomplete HSFETCH
autocomplete HSPOST

