../../../bin/f2py,sha256=wOIOGUFQkgQLtB6-DvpuwiLxiErBIzZHTCuF7DyRXG0,335
../../../bin/numpy-config,sha256=02XhaV3OjCn0JYS2JMP9LBBNS7oTLcV991vNHnquQ34,335
numpy-2.2.6.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
numpy-2.2.6.dist-info/LICENSE.txt,sha256=wAK9Jt59x6pGQlCg3gY9WP5Vl0RS5DieXCHDUKggvwY,47755
numpy-2.2.6.dist-info/METADATA,sha256=ItZI9ThIQpRkymQ7QNc7Sakg1Hh2_HfDldTcpOg0kEo,62026
numpy-2.2.6.dist-info/RECORD,,
numpy-2.2.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-2.2.6.dist-info/WHEEL,sha256=3qIDcXCk577AXiK3pDifO-gE9U_MYWYGgtD78gLa2_U,137
numpy-2.2.6.dist-info/entry_points.txt,sha256=4mXDNhJDQ9GHqMBeRJ8B3PlixTFmkXGqU3RVuac20q0,172
numpy.libs/libgfortran-040039e1-0352e75f.so.5.0.0,sha256=xgkASOzMdjUiwS7wFvgdprYnyzoET1XPBHmoOcQcCYA,2833617
numpy.libs/libquadmath-96973f99-934c22de.so.0.0.0,sha256=btUTf0Enga14Y0OftUNhP2ILQ8MrYykqACkkYWL1u8Y,250985
numpy.libs/libscipy_openblas64_-56d6093b.so,sha256=C9gV0EtrVJkOPMzHUo-7aWRW0JVp9TPQOQwT8M3E3Uo,25021457
numpy/__config__.py,sha256=ISxXZqUJzdJqLA_3gSPKyVVP8ptwRpJ8LPzF0WvKgSI,5277
numpy/__config__.pyi,sha256=ZKpaYX_mDS5X5VwNaH5wNAVi3X1FP0XkI5LcFOImNPk,2377
numpy/__init__.cython-30.pxd,sha256=cWfkT3NMZ3X0V3kjbEeS6qZIhR_eBNVYdO_8Mp-ebGk,46791
numpy/__init__.pxd,sha256=pPfD2-RbhOQKZ7IgCkduBgGYZELhvycWeESYSFh_274,43427
numpy/__init__.py,sha256=auF7BwwPcKjjytiaUQolaULlofN-pf6xIM7BZ-0qYjY,22147
numpy/__init__.pyi,sha256=ofWyIU0AzmaLAVNQmCmOEDgOVzffZEyXXE3PmjWOeIs,211670
numpy/_array_api_info.py,sha256=qiHJDVG58rAk1iTlXsFrnhZ7Y-ghPUkyBpJiMvPK2jg,10381
numpy/_array_api_info.pyi,sha256=P71pudeW0DUFIlo27p5NHC8hoxkYP2ZhrsoS9uEJcvo,4892
numpy/_configtool.py,sha256=asiPfz_TX2Dp0msoNjG43pZKRYgNYusSIg2ieczK8as,1007
numpy/_configtool.pyi,sha256=d4f22QGwpb1ZtDk-1Sn72ftvo4incC5E2JAikmjzfJI,24
numpy/_core/__init__.py,sha256=H95-zST0CH6pnnObjXUXXiPgtub9M35IBGaYE-q4wrU,5612
numpy/_core/__init__.pyi,sha256=Mj2I4BtqBVNUZVs5o1T58Z7wSaWjfhX0nCl-a0ULjgA,86
numpy/_core/_add_newdocs.py,sha256=stKVrZWkWH-g_mp7MwO-N1DkMfKXjLxBsrlurBXmeA4,208755
numpy/_core/_add_newdocs.pyi,sha256=r__d_-GHkfjzuZ0qyjDztsKgdc1eIyeN-cBoYVgMBuo,168
numpy/_core/_add_newdocs_scalars.py,sha256=ePmas0mI6OCpq9W8ZszXHyEhktsBUj_hvEt6ozb8Zic,12603
numpy/_core/_add_newdocs_scalars.pyi,sha256=ZnIk0TgL0szrv6SPCH-4dF469Q_92UvV5_ek47Oj7HM,573
numpy/_core/_asarray.py,sha256=7oZPqNjuDL0IxIeT7V_UJW19lsKS3eny-jlN4Ha-hoA,3912
numpy/_core/_asarray.pyi,sha256=xkgqEh4c9lcFktJija8a1w5Tj7-2XfZOV8GjDZsXpzY,1085
numpy/_core/_dtype.py,sha256=4Pz6KJQJRywlsMhdH8NbIugziDyQi1ekv2ZMw7zomzo,10734
numpy/_core/_dtype.pyi,sha256=DKUAq45hxO7xO6zVuI6oYkkl1gtodB2z0NJ9JtFNhfc,1951
numpy/_core/_dtype_ctypes.py,sha256=dcZHQ46qjV0n7l934WIYw7kv-1HoHxelu50oIIX7GWU,3718
numpy/_core/_dtype_ctypes.pyi,sha256=VwEZFViCPuHlCURv2jpJp9sbHh2hYUpzC_FRZNNGMMw,3682
numpy/_core/_exceptions.py,sha256=dZWKqfdLRvJvbAEG_fof_8ikEKxjakADMty1kLC_l_M,5379
numpy/_core/_exceptions.pyi,sha256=xH30RJw6Yi0lyJzcwb32uSS7aMT64Kf1Cr82ZNCu9jQ,2146
numpy/_core/_internal.py,sha256=B8t6mxvaDouxE-COR010v4_PUHNzOF8mHgFatRPlJWk,29164
numpy/_core/_internal.pyi,sha256=QKaBqSkdl1mnHLJb376B_5mv-GCZtmn8DXDQufA1D4E,2654
numpy/_core/_machar.py,sha256=399tphFPGzJy1bpbeXLDjUUZTebWto1lozB1praORfE,11565
numpy/_core/_machar.pyi,sha256=xH30RJw6Yi0lyJzcwb32uSS7aMT64Kf1Cr82ZNCu9jQ,2146
numpy/_core/_methods.py,sha256=pjmP1yAbtVesXTytuupGIXojO55y8LBS-8fEQPusNIU,9469
numpy/_core/_methods.pyi,sha256=-WJkb43KYhpq59UpUxsjTIB30WAIApHjmlBzXFMrc8Y,556
numpy/_core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so,sha256=sWfWfyklBTonORW_uicvBc4RRpnauzMQu4YPnUyGGdw,178888
numpy/_core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so,sha256=5iQQguqrAdD2iytmFSLsudXSFgkVEE34sRLMFh36GLQ,10510625
numpy/_core/_operand_flag_tests.cpython-312-x86_64-linux-gnu.so,sha256=4TpRx3XIlThfYr2EyAfFB7zRxId8iOK_HYpVEwsasX8,16984
numpy/_core/_rational_tests.cpython-312-x86_64-linux-gnu.so,sha256=47Fmzv1V5uHVoclDxkyOYr2C3teLr3K6Uu3gdY3jY1g,59832
numpy/_core/_simd.cpython-312-x86_64-linux-gnu.so,sha256=mIi7KpJ0Mt0JD0JrBDZS3Wh2kttsOmrzSG0879UO4u8,3042312
numpy/_core/_simd.pyi,sha256=2z2sFPgXr3KRzHltbt31HVrhkXM0VwXFp1lUjxaRMAM,669
numpy/_core/_string_helpers.py,sha256=gu3x0dEnRnh3mnOkviX17r8rCmagVgYHfxILt9Q9irA,2837
numpy/_core/_string_helpers.pyi,sha256=xLlLKJHutEYzyKnTG2k7clcWvVUTvD319SjnKmDXuac,358
numpy/_core/_struct_ufunc_tests.cpython-312-x86_64-linux-gnu.so,sha256=ciEUhFYUHL1n5AVwuSRmk1wpvsxI70ZnIe_ffEkajHY,17120
numpy/_core/_type_aliases.py,sha256=4AU_cVekBKDIXO1URlOQKsMz8nrDw1tHr_nBdQzvNzo,3489
numpy/_core/_type_aliases.pyi,sha256=9nNzq_Bxy5ikgnRaFMEcbThUVrb4HYJQvH58gXDWCGE,2400
numpy/_core/_ufunc_config.py,sha256=LlFpTUnHFeHQlNFiuBHvrqVn-nQ7EvIgUEn3HUclt7k,15030
numpy/_core/_ufunc_config.pyi,sha256=piQY1VeYD5rKJUOOMYRvLhNPMAdLEik4Yzgx-ioB19A,1172
numpy/_core/_umath_tests.cpython-312-x86_64-linux-gnu.so,sha256=dIaC4wrKI9gy4FcSNeu8ztU6SoMWPLEiMUwbbXRqrmU,50512
numpy/_core/arrayprint.py,sha256=s5lMLv3Wy_fa3hB1OqUaM4h1Ja9SB_X-3zAkQW1Tu4E,64812
numpy/_core/arrayprint.pyi,sha256=BmhTDgihJN2U08C0RUzvl36lXp8Cc1CF-uy3MuF3kbI,6934
numpy/_core/cversions.py,sha256=H_iNIpx9-hY1cQNxqjT2d_5SXZhJbMo_caq4_q6LB7I,347
numpy/_core/defchararray.py,sha256=hwzNR5D4bYTDU846j0uKoTnRsZk22jmM5KeXZitkvmU,37798
numpy/_core/defchararray.pyi,sha256=n4P-zXnU8SdMf1cAiKDnJA08L_sVsvoDx7ONFOO-8YM,26962
numpy/_core/einsumfunc.py,sha256=xsYoawvzK4EA2QIYdtk5KyrFkUCe4kSt5wOtXCm_v1s,52820
numpy/_core/einsumfunc.pyi,sha256=mx5u6i7mdFuJH4MqLZVU26-ld5y0x5B9ln6lw9RpW-w,4929
numpy/_core/fromnumeric.py,sha256=gK8m2Y3lSJ5qszgNE-F-ZdN6uab40cW5BBsD4SONHLA,143907
numpy/_core/fromnumeric.pyi,sha256=VLvvO7t4JLBXEX2EHwSjFOQpDfN_ss9VOLwpgc9NwkQ,41190
numpy/_core/function_base.py,sha256=hfcYdavNeeDbiYjvTBqDA6OJHxH1fuNDdMtTZUi3RZg,19733
numpy/_core/function_base.pyi,sha256=_pJUw_NYCDy1EyGL0ABeXAWNOTsj_n7L_8GHPoqPfYs,5690
numpy/_core/getlimits.py,sha256=Uy3W6eJwu2l7R6ovqdfeOyQybF5jjlPER88pSM3_JPg,26112
numpy/_core/getlimits.pyi,sha256=q30hQ3wDenmxoZUSoSOqyVrZZVGlsixXCHe6QUthbp8,61
numpy/_core/include/numpy/__multiarray_api.c,sha256=u7HxPIx7xdxAPTE0gristUOO0-1L-_fl0IeKqR4voxI,12669
numpy/_core/include/numpy/__multiarray_api.h,sha256=akdAXdNQvHxPFPbdeobhoGzyLUkoVdwzKDjzdbtk5zQ,61383
numpy/_core/include/numpy/__ufunc_api.c,sha256=Fg7WlH4Ow6jETKRArVL_QF11ABKYz1VpOve56_U3E0w,1755
numpy/_core/include/numpy/__ufunc_api.h,sha256=tayZuDCeuqm3ggFvWxJuoARz5obz6Saas9L7JcKO_eQ,13166
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s-Hw_l5WRwKtYvsiIghF0bg-mA_CgWnzFFOYVFJ-q4k,1857
numpy/_core/include/numpy/_numpyconfig.h,sha256=brqqDI4gwfGEFHMIWi0oNA0n_qnBBUWFVJtgfcdpSA0,926
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=n6_Kb98SyvsR_X7stiNA6VuGp_c5W1e4fMVcJdO0wis,4574
numpy/_core/include/numpy/arrayobject.h,sha256=mU5vpcQ95PH1j3bp8KYhJOFHB-GxwRjSUsR7nxlTSRk,204
numpy/_core/include/numpy/arrayscalars.h,sha256=LlyrZIa_5td11BfqfMCv1hYbiG6__zxxGv1MRj8uIVo,4243
numpy/_core/include/numpy/dtype_api.h,sha256=Gn37RzObmcTsL6YUYY9aG22Ct8F-r4ZaC53NPFqaIso,19238
numpy/_core/include/numpy/halffloat.h,sha256=TRZfXgipa-dFppX2uNgkrjrPli-1BfJtadWjAembJ4s,1959
numpy/_core/include/numpy/ndarrayobject.h,sha256=MnykWmchyS05ler_ZyhFIr_0j6c0IcndEi3X3n0ZWDk,12057
numpy/_core/include/numpy/ndarraytypes.h,sha256=qnnC60F-oeGzkM65vV8VcMsThLYKcDWkhLQBOfJ3jZk,65053
numpy/_core/include/numpy/npy_1_7_deprecated_api.h,sha256=90kGcNaBPgT5FJArB_MPgW24_Mpl5RcfUR3Y0rRB5Bw,3746
numpy/_core/include/numpy/npy_2_compat.h,sha256=wdjB7_-AtW3op67Xbj3EVH6apSF7cRG6h3c5hBz-YMs,8546
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=eE9dV_Iq3jEfGGJFH_pQjJnvC6eQ12WgOB7cZMmHByE,857
numpy/_core/include/numpy/npy_3kcompat.h,sha256=grN6W1n7benj3F2pSAOpl_s6vn1Y50QfAP-DaleD7cA,9648
numpy/_core/include/numpy/npy_common.h,sha256=wbV1Z6m3w1h4qVcOxfF38s3H13UfFHEuBGRfDhTeUKE,36551
numpy/_core/include/numpy/npy_cpu.h,sha256=AUJ5CqlguteR3-R0IjPt5rylWtvvccCWtt0GpjZbexU,4703
numpy/_core/include/numpy/npy_endian.h,sha256=vvK7ZlOt0vgqTVrIyviWzoxQz70S-BvflS4Z_k6X5XE,2834
numpy/_core/include/numpy/npy_math.h,sha256=aeSFs60QbWPy1gIPyHDPrYExifm5mbDAcjP_mLk_PF0,18858
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=0yZrJcQEJ6MCHJInQk5TP9_qZ4t7EfBuoLOJ34IlJd4,678
numpy/_core/include/numpy/npy_os.h,sha256=hlQsg_7-RkvS3s8OM8KXy99xxyJbCm-W1AYVcdnO1cw,1256
numpy/_core/include/numpy/numpyconfig.h,sha256=OvRlre4eb9KBWt6gAE5cQ4K-P2uRmIKU1rAKxWFygmA,7161
numpy/_core/include/numpy/random/LICENSE.txt,sha256=-8U59H0M-DvGE3gID7hz1cFGMBJsrL_nVANcOSbapew,1018
numpy/_core/include/numpy/random/bitgen.h,sha256=49AwKOR552r-NkhuSOF1usb_URiMSRMvD22JF5pKIng,488
numpy/_core/include/numpy/random/distributions.h,sha256=W5tOyETd0m1W0GdaZ5dJP8fKlBtsTpG23V2Zlmrlqpg,9861
numpy/_core/include/numpy/random/libdivide.h,sha256=ew9MNhPQd1LsCZiWiFmj9IZ7yOnA3HKOXffDeR9X1jw,80138
numpy/_core/include/numpy/ufuncobject.h,sha256=r2XM6XyILKXLqgmHFVu8jXqvOp_Zv8tLfS8Omn5jbng,11918
numpy/_core/include/numpy/utils.h,sha256=wMNomSH3Dfj0q78PrjLVtFtN-FPo7UJ4o0ifCUO-6Es,1185
numpy/_core/lib/libnpymath.a,sha256=Rg3gCXTxpny2Hh-jZFKh6KDYquzjcJklumEnLHhQXQ0,118712
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=_LsWV1eStNqwhdiYPa2538GL46dnfVwT4MrI1zbsoFw,147
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=0iMzarBfkkZ_EXO95_kz-SHZRcNIEwIeOjE_esVBkRQ,361
numpy/_core/lib/pkgconfig/numpy.pc,sha256=TixQH5uvO6LvobGP1BrUfGuCdMxvdphpZUy_KczizY4,191
numpy/_core/memmap.py,sha256=B3k5EZ8QwzjPwWwOtVRVyEofQJSG_pCBcWCFknO-GaU,12664
numpy/_core/memmap.pyi,sha256=_LKjb_PuhcQwpqc2lFaL379DYzQ9PtuKdlVV3jXOYEM,47
numpy/_core/multiarray.py,sha256=0P7ZBHKR0mI0tatyqDCHwnfewEEiQ48dwrzVL2PQAk0,58137
numpy/_core/multiarray.pyi,sha256=MzZt-K2x2BrDebbirvR2so5Tbipw3u189VVsDjkAdyk,33396
numpy/_core/numeric.py,sha256=vsiEcMig4QHwMf9HP5maqOhFsblqGbh8AtE4cX08D7w,81726
numpy/_core/numeric.pyi,sha256=Ax8B42cDqwsrlVsYt6NhthEZvQNMGdNzNcpXAy9i1kw,19198
numpy/_core/numerictypes.py,sha256=W-Eu_Av5zNNBHDZHGLNlmNb2UuMByKRdwWPTsJxA5oQ,16125
numpy/_core/numerictypes.pyi,sha256=ZlPCN6_8qJ7ZWiDayxfQ9oRnq1Gej2P388BX30SUR7s,3533
numpy/_core/overrides.py,sha256=czubu5JHSdid31If0WLqOYEM3WiAY03tLmyoa04sWjg,7211
numpy/_core/overrides.pyi,sha256=4ycXYFRjEycSPOaRtciFKwBTsAjHUoGztX-IkTpXQYw,1743
numpy/_core/printoptions.py,sha256=FUY--hG0-oobvtHOY64D50Bs_-JFmf-Nza7C9IXORFY,1063
numpy/_core/printoptions.pyi,sha256=eNiliCnDuZBxla6X9kwZ-7YiCn-UtMbT-U_qTnw8l9w,594
numpy/_core/records.py,sha256=0Uv2Z2xBvxYQUsAhp5zZ262YFHmSN6R_bazI_EyRE00,36862
numpy/_core/records.pyi,sha256=nDLqj-5Z8hE8hQ4HiDPuHqGy91srd4Vvpn3_ZbEk2o4,8789
numpy/_core/shape_base.py,sha256=CK3LrcfWKnChokUm1eHYsL43Q7D5qPq7QxDPuYYMnAU,32883
numpy/_core/shape_base.pyi,sha256=4UDvO6R4ZtZChKb4HNZUil3P8HOGTM5wOwPMT8tsp6Y,4545
numpy/_core/strings.py,sha256=wCfZ3b3_WKY3LZ0cPn5IXrTN6wXtCSfa8Jrvpznz85c,45672
numpy/_core/strings.pyi,sha256=NRCCxdkiEvQO9I__b-rNef4pxiqrCgtIFvyPvs7rLng,12770
numpy/_core/tests/_locales.py,sha256=_J4MFSLUG1hiIfiifglI0nD--lS3CqwIjKKM3is0S6Q,2176
numpy/_core/tests/_natype.py,sha256=9N-pE9LuQKrqT7ef-P9mtXpWls3YAsZ8JR-3cR7TRjs,6259
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=BQakB5o8Mq60zex5ovVO0IatNa7xbF8JvXmtk6373So,5842
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=pxWwOaGGahaRd-AlAidDfocLyrAiDp0whf5hC7hYwqM,967
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=yBlz8r6RnnAYhdlobzGGo2FKY-DoSTQaP26y8138a3I,61365
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=0GXe7XG1Z3jXAcK-OlEot_Df3MetDQSlbm3MJ__iMQk,61365
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=w_Sv2NDn-mLZSAqb56JT2g4bqBzxYAihedWxHuf82uU,61339
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=DZrMYoZZZyM1DDyXNUxSlzx6bOgajnRSLWAzxcPck8k,60289
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=0aosXZ-9DYTop0lj4bfcBNwYVvjZdW13hbMRTRRTmV0,60305
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=HEK9ePx1OkKrXIKkMUV0IxrmsDqIlgKddiI-LvF2J20,61339
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=v855MTZih-fZp_GuEDst2qaIsxU4a7vlAbeIJy2xKpc,60846
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=0PNnDqKkokZ7ERVDgbes8KNZc-ISJrZUlVZc5LkW18E,59122
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=JKC4nKr3wTzA_XNSiQvVUq9zkYy4djvtu2-j4ZZ_7Oc,60869
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=rUAWIbvyeKh9rPfp2n0Zq7AKq_nvHpgbgzLjAllhsek,17491
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=djosT-3fTpiN_f_2WOumgMuuKgC_XhpVO-QsUFwI6uU,58624
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=K7jL6N4KQGX71fj5hvYkzcMXk7MmQes8FwrNfyrPpgU,60299
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=ynzbVbKxFzxWFwxHnxX7Fpm-va09oI3oK1_lTe19g4w,11692
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=NOBD-rOWI_FPG4Vmbzu3JtX9UA838f2AaDFA-waiqGA,68922
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=tdbYWPqWIz8BEbIyklynh_tpQJzo970Edd4ek6DsPb8,60303
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=39EUD0vFMbwyoXoOhgCmid6NeEAQU7Ff7QFjPsVObIE,68917
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=8PUjnQ_YfmxFb42XJrvpvmkeSpEOlEXSmNvIK4VgfAM,58611
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=XOsBUuPcMjiO_pevMalpmd0iRv2gmnh9u7bV9ZLLg8I,60293
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=Hv2WUMIscfvQJ5Y5BipuHk4oE4VY6QKbQp_kNRdCqYQ,60299
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=iolZF_MOyWRgYSa-SsD4df5mnyFK18zrICI740SWoTc,60299
numpy/_core/tests/examples/cython/checks.pyx,sha256=7wt61LhY_j0ZPzKcWDKnnbtDR8PoHmQixpFYNlCwMOM,7900
numpy/_core/tests/examples/cython/meson.build,sha256=uuXVPKemNVMQ5MiEDqS4BXhwGHa96JHjS50WxZuJS_8,1268
numpy/_core/tests/examples/cython/setup.py,sha256=6k4eEMjzjXPhGAW440qpMp2S2l5Ltv-e9e-FnVnzl3w,857
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=htSR9ER3S8AJqv4EZMsrxQ-SufTIlXNpuFI6MXQs87w,346
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=1q4I59pdkCmMhLcYngN_XwQnPoLmDEo1uTGnhrLRjDc,203
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=ltBLbrl1g9XxD2wvN_-g3NhIizc8mxnh2Z6wCyXo-8E,452
numpy/_core/tests/examples/limited_api/meson.build,sha256=YM5RwW_waFymlWSHFhCCOHO6KCknooN0jCiqScL0i5M,1627
numpy/_core/tests/examples/limited_api/setup.py,sha256=p2w7F1ardi_GRXSrnNIR8W1oeH_pgmw_1P2wS0A2I6M,435
numpy/_core/tests/test__exceptions.py,sha256=PA9MhiaEITLOaIe86lnOwqAa3RFrA5Ra4IrqKXF-nMU,2881
numpy/_core/tests/test_abc.py,sha256=mIZtCZ8PEIOd6pxLqdUws3wMfXUjsVO3vOE9vK5YPd8,2221
numpy/_core/tests/test_api.py,sha256=aCh293oLPnbK7gi0PW_ilL9Gcr6-3UpO0MMzS39D8Sc,22930
numpy/_core/tests/test_argparse.py,sha256=DRLQD5TxhudrQZ79hm5ds3eKsXh_Ub7QsvEYzsdDSX0,2824
numpy/_core/tests/test_array_api_info.py,sha256=4CpUWnch1EtLojYabVAF7n_-Fks3QTODHERL2FzR1Ps,3062
numpy/_core/tests/test_array_coercion.py,sha256=p-qWx0wju9JIwIC3wUrVFUJpi5FeOD88OljtzTzndmk,34833
numpy/_core/tests/test_array_interface.py,sha256=9ND3Y00rgdBSgst5555zrzkvdWzZ4vZgWJOw3djXZAk,7767
numpy/_core/tests/test_arraymethod.py,sha256=SL2PN10yYMp6C8CnKEykjit8QBtVBIGwbTPDdSDpCLY,3253
numpy/_core/tests/test_arrayobject.py,sha256=aVv2eGjunCMEDFgmFujxMpk4xb-zo1MQrFcwQLfblx0,2596
numpy/_core/tests/test_arrayprint.py,sha256=NKFx165-YwIw-sf7et1_M1cQ2V4t6nh8JN5N4GiohYw,49068
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=nnBEgeRIENrOOZvTzRK7SRYYW9dD6E6npDmIuN0ggCc,5074
numpy/_core/tests/test_casting_unittests.py,sha256=iXHJR9sjpKk37toV9TMDYJAErVgqOxxEM-SEGOvdyF8,34308
numpy/_core/tests/test_conversion_utils.py,sha256=fpduQ79yLpvZ8fdLs4H0CCsBEh3TlZs3SMr-lUQ6pTg,6605
numpy/_core/tests/test_cpu_dispatcher.py,sha256=nqlgFk-Ocfgc18g-b4fprYssfcpReiyvgbWPzsNEoFI,1552
numpy/_core/tests/test_cpu_features.py,sha256=WcKrpR7sPZkF7V-tALki9KfRaEJedE3WpA9AfXNE2Dw,15419
numpy/_core/tests/test_custom_dtypes.py,sha256=_T9kvGbPJzjLnAtGqoRIeXQNjEuBgJ2DvLN6lrb-fJA,11623
numpy/_core/tests/test_cython.py,sha256=G3usNUppvqvlbLqTBREn2II9_bhdlxfuZTg8EFd2LpU,8619
numpy/_core/tests/test_datetime.py,sha256=KD9WAcYjDoa_dujH3lUQukb3IMyyPy2Gkf2oHm6sdOg,121671
numpy/_core/tests/test_defchararray.py,sha256=tLrnS4oEVDwjbx74fHyi9r43yAE0J7mJZVfdeHvlSJg,30601
numpy/_core/tests/test_deprecations.py,sha256=q6yJhSODzcbx6LmQzHJqtFKsW4_xfuuy0BC-RK4t6mI,28510
numpy/_core/tests/test_dlpack.py,sha256=SQCgw4Ya2iYwEjEVJ0p_XvSYNKY2h_eygTmZp8-T4F8,5801
numpy/_core/tests/test_dtype.py,sha256=lEpYwt2LZ0MWH3jGliiwLkeoqSi0iNI-KSEoAIwH9cg,77402
numpy/_core/tests/test_einsum.py,sha256=zcFC5OFRGZA9r53gXKmFZUQV0o_T1BkdTXLZ8vG0YLA,52890
numpy/_core/tests/test_errstate.py,sha256=5YUzK95WyepGyaJ4nkkXLUiHziNBoU0SFBHjMn5U7G0,4634
numpy/_core/tests/test_extint128.py,sha256=tVrw3jMHQkA0ebk7Pnq33I5Yu9V24KNHotYIG3V8ds0,5644
numpy/_core/tests/test_function_base.py,sha256=L_toIAG1hbAiOcRxIiLX7yxK4E-hqVMqdUGCBhg9dMQ,17462
numpy/_core/tests/test_getlimits.py,sha256=xMcjRyx_hAwR-Q3qTcZSFhneZtIXp6u7KOsihUu7-Yg,6977
numpy/_core/tests/test_half.py,sha256=EFzZNaNNY_H1hd3dSPBZ2wZt3E67D6KpDE3YaOMx_XY,24313
numpy/_core/tests/test_hashtable.py,sha256=Ws1EeQWCf7vz8G_VsFTIZUVI-hgKXUEAbtQpvoBjBHo,1147
numpy/_core/tests/test_indexerrors.py,sha256=wvatr7JlqAAYv-hHAAT-9DwUCnRcKiJ9qLcl6aKe9RU,4734
numpy/_core/tests/test_indexing.py,sha256=xjJGHu7eZT_KX_LAL-8UBTFTxqFwZoJUZetQVrbjJ7g,55297
numpy/_core/tests/test_item_selection.py,sha256=kI30kiX8mIrZYPn0jw3lGGw1ruZF4PpE9zw-aai9EPA,6458
numpy/_core/tests/test_limited_api.py,sha256=ndfWEX3X4s6EqWSTDJzdOe0DDQGH7SqnTnYjce0cYh4,3304
numpy/_core/tests/test_longdouble.py,sha256=H7VeOyaLfSMHClUDSKloOuHiDbZxeoypJnc5AtsM4xw,13890
numpy/_core/tests/test_machar.py,sha256=eDTrzJgwfaus0Ts86-HR9YkAPOwOSOPImPTHugn1EOc,1069
numpy/_core/tests/test_mem_overlap.py,sha256=jM7NXE3N_bOjgP9vMqyzzcIXJwbIREXiRK41iciggAA,29138
numpy/_core/tests/test_mem_policy.py,sha256=JFou_8xT0-cwccZEQfaingaktY-RH3hrUJZa2_b7t2o,16660
numpy/_core/tests/test_memmap.py,sha256=LQ4NBQe8s_5DMN5yCeY9dpqTeDBOge6TKN6xxMwCbRI,8142
numpy/_core/tests/test_multiarray.py,sha256=YsNiZInPpHR1o6-sv7pq9sg4GW5J_v9KCnu1TNuDMIo,392270
numpy/_core/tests/test_multithreading.py,sha256=DnSUipGmHE3YMI9Dgxfplo1HWyf1sjQgCcHIy41dTL4,8606
numpy/_core/tests/test_nditer.py,sha256=o-YxH56efHb_yN5-kbJ3mDVpp4Vasa_DPE5lhEzcAc0,131186
numpy/_core/tests/test_nep50_promotions.py,sha256=96WpsYYNdlaszFOCLmxHCg3iOHna4VPPxHZjdRp1lVU,10064
numpy/_core/tests/test_numeric.py,sha256=_f1nQWujm2PQZF4Y9Gjxt4W7R0MbVNGJh9OEdjkKFCE,158490
numpy/_core/tests/test_numerictypes.py,sha256=aADiXLPAkgAFF80_tRczhuH6lVyMLcA3k_AbGcDemp4,23292
numpy/_core/tests/test_overrides.py,sha256=evrJX5mAWquq0lD6qM2Hn4_1_mkSk8cpNzUj6_QcZFE,27936
numpy/_core/tests/test_print.py,sha256=mzUSbQ2kSa1aDl7NRUexj5UG4IM4zaZ-5EIoEoXhA_Q,6836
numpy/_core/tests/test_protocols.py,sha256=6pxSZKmde5KHoN3iEMKReAFHrMldAm3ZZQwVh_kQ9Uw,1189
numpy/_core/tests/test_records.py,sha256=eyDJb-oglohhgW4b4sZwe-_1PABhkM9_7a9qU3n7oAU,20534
numpy/_core/tests/test_regression.py,sha256=o4FwvndFMYHPqYQgKuEjgIpoizZ3vvQ-3HIuHqiRD6g,95395
numpy/_core/tests/test_scalar_ctors.py,sha256=3mhZlumKJs5WazhPgATWf5Y4E4POQy-bcUBSEt5pasc,6719
numpy/_core/tests/test_scalar_methods.py,sha256=u0Bn-6-mSpOc_mP0C7BHpg3RbGWnsb_zZR1Ooubno2Y,9142
numpy/_core/tests/test_scalarbuffer.py,sha256=EdiF5tVrZXDchoK0P5sbQgluyyYQCIrLCaxvafaCKNk,5582
numpy/_core/tests/test_scalarinherit.py,sha256=XbCvtSSEU_c3cHi9Nxg7nt7itZdnREEh0sdqDUU4-ek,2588
numpy/_core/tests/test_scalarmath.py,sha256=AKHil07nk1xDgcEUUvA3wRDR-xZjWwE2k1zvv6knOYI,46631
numpy/_core/tests/test_scalarprint.py,sha256=UGxofUYFo3tqu-2fgaNgmS8K-uwhwv7X3tu7Pu7yeQQ,20635
numpy/_core/tests/test_shape_base.py,sha256=u9ozYhzM-V0GINYi04jYeNsjiD7XtssrD29zhFVaOA0,30982
numpy/_core/tests/test_simd.py,sha256=DJ-N-Q7E29VBY4VYmQWTR4XzRcxQKptCke5CwxCl_aw,48650
numpy/_core/tests/test_simd_module.py,sha256=g0XWjB1TE4E0y4McOjkZKhR7OB-K01eqy4pJcGfU2zg,3903
numpy/_core/tests/test_stringdtype.py,sha256=wgIoYRYlP-2Q2Z7PKBOo76fGbzKewxwN98IECfmBHiM,57658
numpy/_core/tests/test_strings.py,sha256=UCq7ActPLrMNRG7BDHfrOH52MOZ3uy3Tp35T3-hQV00,51787
numpy/_core/tests/test_ufunc.py,sha256=qiV2wkNG0-Z2_u_aveRLKI9qEOqaLxyWvch6Btz-Urc,132405
numpy/_core/tests/test_umath.py,sha256=qeEIqnjBl7_mHhBZh6-S_7oBz94nabMyS-xqdQOXl9o,193188
numpy/_core/tests/test_umath_accuracy.py,sha256=TRSzuQJ2kN2D3BUQ3IX1WhhT6ttIKvnnaMaaqU-A7ug,5472
numpy/_core/tests/test_umath_complex.py,sha256=pWRHpzBodvDGoKG1gkRAKJ1uPxQ_fV_VqIm77SD0BlA,23290
numpy/_core/tests/test_unicode.py,sha256=LotRRPbJke99uyy3uY3rAteaJMMiYpSzcOmargPNKIc,12854
numpy/_core/umath.py,sha256=OsbavmLRxKNNbV8SnPEc3mVNk9EIVjMhZeRs9nCUsTU,2093
numpy/_core/umath.pyi,sha256=FIqmlQwQIueIrs-_QehV3guNEnJE2LxVs3NPCj38Vdo,2643
numpy/_distributor_init.py,sha256=IKy2THwmu5UgBjtVbwbD9H-Ap8uaUJoPJ2btQ4Jatdo,407
numpy/_distributor_init.pyi,sha256=6IvMzAmr0-Z6oqTkZcgXgrkJrQXVMjBih2AZvLdDgOE,27
numpy/_expired_attrs_2_0.py,sha256=ZTV3IpeE4UcJSn2RTZLxKAixEl557MkKK4R7xubw1Rw,3903
numpy/_expired_attrs_2_0.pyi,sha256=dDjT_qRjSq9m3_DcfOJlRaTzFtHacBVRdkYgA0Weeho,1269
numpy/_globals.py,sha256=XVuUPpFLueqKUTNwqiOjWWahnM-vGxGy4tYA3ph-EAE,3090
numpy/_globals.pyi,sha256=IrHHIXmibXzgK0VUlECQLw4IEkveXSHo_ZWnTkfnLe4,280
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/hook-numpy.py,sha256=Ood-XcWlQQkk90SY0yDg7RKsUFVGwas9TqI-Gbc58_s,1393
numpy/_pyinstaller/hook-numpy.pyi,sha256=tAvtMPovoi-sur0D1NAo3_evSmYKLTh0bgRSC7QrCIk,349
numpy/_pyinstaller/tests/__init__.py,sha256=IJtzzjPSw419P-c2T4OT48p-Zu4JohoF9svWqhDshgk,329
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=6iL-eHMQaG3rxnS5EgcvrCqElm9aKL07Cjr1FZJSXls,1143
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=8K-7QxmfoXCG0NwR0bhIgCNrDjGlrTzWnrR1sR8btgU,1135
numpy/_pytesttester.py,sha256=itUxMEXdYQT_mCYqde-7CqjxOA59wiLFyJeIyoVtGgI,6325
numpy/_pytesttester.pyi,sha256=fRkDNxl5obspW99ujQV3NDXrROXxDiLVFyj8Aew_zyk,497
numpy/_typing/__init__.py,sha256=0y70ouUj2_lKGcA6vGbm_NWVR48xKoyu_c7rom_AEp4,5047
numpy/_typing/_add_docstring.py,sha256=GHU_gjWt_A6x7RIcztvfayVCs78Kgi8IeNKJZyfWkWg,3995
numpy/_typing/_array_like.py,sha256=QD4uxTyvuMERM5AhE6PxzdL5yHUMb2UOi7HdQFFNXoI,5565
numpy/_typing/_callable.pyi,sha256=bogxuArAdYY-IGWzw0-ayhdb5-P8YhHXK-J0TX_j38g,11811
numpy/_typing/_char_codes.py,sha256=RJSvAIAy8TAEQbFfoDNouUdLcYngmBmV4X7At62SUbU,8786
numpy/_typing/_dtype_like.py,sha256=3q7Me_RXr75ba4p1vPy-nw5NRLWsnCnHsfzVGnZMNig,5964
numpy/_typing/_extended_precision.py,sha256=dGios-1k-QBGew7YFzONZTzVWxz-aYAaqlccl2_h5Bo,777
numpy/_typing/_nbit.py,sha256=LiAPuMPddJ9CjSStw8zvXQ1m_FbNIzl_iMygO851M0g,632
numpy/_typing/_nbit_base.py,sha256=HHn2zYWN-3wLsyigd97cs9uyI3NvRYUcQ69OLOdC-ks,2880
numpy/_typing/_nested_sequence.py,sha256=7idN0EyEI6Nt0VH9xnWVj4syqeu_LK8IESZwczVcK1g,2608
numpy/_typing/_scalars.py,sha256=9v-1xahC9TZg28FTfBG15vWCcnDB1bfWz7ejT0eDrVw,1031
numpy/_typing/_shape.py,sha256=fY1qi6UDFjPW1b4GaxhcJ9tRAQu6SXLZINd_Vy60XSY,231
numpy/_typing/_ufunc.py,sha256=U6OCdDLHzXSt1fbSldHFP0viWHh4u3Y1CDBvzBUY8-M,153
numpy/_typing/_ufunc.pyi,sha256=h4Gs_FASSm7e_lrJWmsJazZOvZMr_N0XSzrVXeA8jAo,26709
numpy/_utils/__init__.py,sha256=fDuc2LsC4olo0utoWjAs3LXux-gPYHFKhThlEIi4eOQ,3291
numpy/_utils/__init__.pyi,sha256=E4kbvhiLuJeW77FvO87VVMcYEazVQy7eTle-7HUU1jc,738
numpy/_utils/_convertions.py,sha256=0xMxdeLOziDmHsRM_8luEh4S-kQdMoMg6GxNDDas69k,329
numpy/_utils/_convertions.pyi,sha256=4l-0UmPCyVA70UJ8WAd2A45HrKFSzgC0sFDBSnKcYiQ,118
numpy/_utils/_inspect.py,sha256=LcbHUJ2KPDpPeNixyIeKOUWvORaLG5J-H0uI3iHIsOA,7435
numpy/_utils/_inspect.pyi,sha256=hqpbcKWZzVkTaMf6loQup3ZMXifIit-A0vSIhD92D88,2255
numpy/_utils/_pep440.py,sha256=Vr7B3QsijR5p6h8YAz2LjNGUyzHUJ5gZ4v26NpZAKDc,14069
numpy/_utils/_pep440.pyi,sha256=xzYJoZ6DnjvgaKr8OsBwim77fAJ0xeQJI9XAt75gvfI,3870
numpy/char/__init__.py,sha256=WGpEng-lsHKxUlmuANY8hKCl3ZC622HYSAFnpf7sgUE,93
numpy/char/__init__.pyi,sha256=s5kfrSM9fwhtbUmzC-KlCoA4AyKpR0GzeS45ZoyQkbA,1539
numpy/compat/__init__.py,sha256=b3rw1J_V3MwU-LZf8uISRKvfXzFaBjFHACbgyLo785Y,727
numpy/compat/py3k.py,sha256=2jk3PPVI2LB1v4mndi0Ydb-ymcgXzJ5G2hIdvoWavAI,3803
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/conftest.py,sha256=G-BY__VpzWy3NY7KjDvHW01CyGkWx-znx2WlhiUShy8,8717
numpy/core/__init__.py,sha256=FWRkekGqZ1NF4YYNfm46mOAO9u3v4ZYts_lc8ygQfqY,1275
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype.py,sha256=3SnNsjxlKobD8Dn8B9egjIQuQLdbWz9OtVAZ4_wlDw8,322
numpy/core/_dtype.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype_ctypes.py,sha256=lLzxauA8PVnopTuGh9USt1nVw2qCI8Z7bL66er3JoHU,350
numpy/core/_dtype_ctypes.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_internal.py,sha256=f3eVtRx2tKrJxxavZNe_f1Ln-_1shhSlfeRZEDTlxhU,947
numpy/core/_multiarray_umath.py,sha256=Yb0HORec_wcEV3RNNU4RZnlATYTUQtjAHMYmL4pvNLs,2096
numpy/core/_utils.py,sha256=5fk18JN43Rg6YHvan6QjdrOeOuLtRlLVmP6MadBEJVA,923
numpy/core/arrayprint.py,sha256=a1DkStlBSsVViSJw523Mm-lboVaAtCloBNCrigyOpbI,338
numpy/core/defchararray.py,sha256=G9S6jkdXegRkXl58hSpPnmndjdym4801Yzq2lzzmApM,346
numpy/core/einsumfunc.py,sha256=px-rSPkwAMbRNmp5uILgVC2QSr73InKFfvW7LSfNGGw,338
numpy/core/fromnumeric.py,sha256=aNquLnfZX1XZRAz5MJza5ZT7IlgJo0TMHlR62YT2biM,342
numpy/core/function_base.py,sha256=Sa9Ec2Y21kPmjn4Xsh7Y1V1c7bUdxYjzixIwHZJ4sCo,350
numpy/core/getlimits.py,sha256=aYJVaVqiSGKuPfSIa7r0MMZMQkJP2NRNJ7Zd2dszygU,334
numpy/core/multiarray.py,sha256=SwVF8KNm29qyaq7vx8rrljNNxfn0e6G5y1H830n1Rac,792
numpy/core/numeric.py,sha256=LSuzJ9OsQ0IEpW2rKlAwuvNypZeDZ0AJDoJOt93XB-k,359
numpy/core/numerictypes.py,sha256=RvhfWFh9KR0SPDNcrAYnW-PO9TKAND75ONXhL5Djs8Q,346
numpy/core/overrides.py,sha256=sWaAgbH_piO0mWDeVqqoqkFqqpPHM87FqOZFJ3AO8lU,334
numpy/core/overrides.pyi,sha256=-3xfjHfa4UaCuhTVwwRN4EOM5uz9vZR0gMeTVvEdbYI,525
numpy/core/records.py,sha256=j9BftQLLljVdcENT41eGflG7DA7miXQ7q3Yf53-zYcY,326
numpy/core/shape_base.py,sha256=MhuxPRwwg5hIdHcJ-LABdQ0oYEYGVxeD-aomaFs9-f4,338
numpy/core/umath.py,sha256=f6KbsWYh5oTj3_FWHip_dr51BdczTAtMqgpn9_eHcz4,318
numpy/ctypeslib.py,sha256=AhgVVThYHjfMEccnSDfH2B3puHU6ZjPwxPcIuFSnzRA,18836
numpy/ctypeslib.pyi,sha256=I21gEirYRu9BQTndIJ_hwOfHbKxs13GL6A6ndpvWT8Y,8088
numpy/doc/ufuncs.py,sha256=9xt8H34GhrXrFq9cWFUGvJFePa9YuH9Tq1DzAnm2E2E,5414
numpy/dtypes.py,sha256=zuPwgC0ijF2oDRAOJ6I9JKhaJuhXFAygByLQaoVtT54,1312
numpy/dtypes.pyi,sha256=BYyUPY0MKF7EzspiOss_FaxqEEzmd0dEpsvGySRfSek,15180
numpy/exceptions.py,sha256=2EH3OwDVoJtAsRODuGlnLWA1hrjDniolCVkR87-eHIo,7838
numpy/exceptions.pyi,sha256=rVue0Qxt3GG40b5xKlj0r_JFjbX6s-bPP7YlqdQlvv0,751
numpy/f2py/__init__.py,sha256=hz6c1M2csKnlKPWbKIDcpSo0cbT5V0UPhQYkELi8zEw,2503
numpy/f2py/__init__.pyi,sha256=uxcZnHA75gxBi50Z3OTWYSYZaeIuWFQv2Dl0F8_WX-g,1061
numpy/f2py/__main__.py,sha256=6i2jVH2fPriV1aocTY_dUFvWK18qa-zjpnISA-OpF3w,130
numpy/f2py/__version__.py,sha256=7HHdjR82FCBmftwMRyrlhcEj-8mGQb6oCH-wlUPH4Nw,34
numpy/f2py/_backends/__init__.py,sha256=7_bA7c_xDpLc4_8vPfH32-Lxn9fcUTgjQ25srdvwvAM,299
numpy/f2py/_backends/_backend.py,sha256=GKb9-UaFszT045vUgVukPs1n97iyyjqahrWKxLOKNYo,1187
numpy/f2py/_backends/_distutils.py,sha256=whJ4xqPet1PffVOcR6W_2NF8yR4LLDh3pZLrKkl0Rh4,2384
numpy/f2py/_backends/_meson.py,sha256=xRGHWhdQJIs1-c3fHeeGHL50XyjU6NjX4-Wp3gjldMY,8089
numpy/f2py/_backends/meson.build.template,sha256=hQeTapAY0xtni5Li-QaEtWx9DH9WDKah2lcEuSZfLLo,1599
numpy/f2py/_isocbind.py,sha256=zaBgpfPNRmxVG3doUIlbZIiyB990MsXiwDabrSj9HnQ,2360
numpy/f2py/_src_pyf.py,sha256=4Qx_-SQSsDh-ggNw3dmHTLASgu1dUY670_Z06WY8clM,7664
numpy/f2py/auxfuncs.py,sha256=PSpBh067SNG1XUJNHqCLpxiorTieeuVTj5h8tOTfXeE,27020
numpy/f2py/capi_maps.py,sha256=MTHjWUSTBngVZtyULdBe1QxAGq9IrxNV8OthujKKr0w,30607
numpy/f2py/cb_rules.py,sha256=fSxXAxjNaPXt54E957v1-Q3oCM06vbST5gFu1D98ic4,25004
numpy/f2py/cfuncs.py,sha256=Jz-em0GDHjexh8FiVEYccAMV4xB5Bp9kQVUMM1uBNcY,52484
numpy/f2py/common_rules.py,sha256=gHB76WypbkVmhaD_RWhy8Od4zDTgj8cbDOdUdIp6PIQ,5131
numpy/f2py/crackfortran.py,sha256=KfTsGcO947ziTwz9UTmrfyXLbqXGIngrMRkso2C0v5E,148095
numpy/f2py/diagnose.py,sha256=7-Turk573zFa1PIZiFPbC4Pukm1X0nF8PyGxnlc08Fc,5197
numpy/f2py/f2py2e.py,sha256=inb09kMkkYig8a6Rizj6xvHuWXfrqZzXh1oZgz0dZvM,28838
numpy/f2py/f90mod_rules.py,sha256=XGtag5pv2Np-hdtjwmSxofKbLO2U_N49sEK_X4Lp3SA,9874
numpy/f2py/func2subr.py,sha256=6d2R5awuHRT4xzgfUfwS7JHTqhhAieSXcENlssD_2c4,10298
numpy/f2py/rules.py,sha256=EhOTqFY3M0UX5dQGlthzkFC0b-R5nCVh4ST4tp0smFY,62938
numpy/f2py/setup.cfg,sha256=Fpn4sjqTl5OT5sp8haqKIRnUcTPZNM6MIvUJBU7BIhg,48
numpy/f2py/src/fortranobject.c,sha256=CYrF44_CoUbZy3QHhe5sAPVtqsaCT4x9oCtUeD7IVyc,46049
numpy/f2py/src/fortranobject.h,sha256=7cfRN_tToAQ1Na13VQ2Kzb2ujMHUAgGsbScnfLVOHqs,5823
numpy/f2py/symbolic.py,sha256=PvP0bK0FLEDQj14u340HJu7ghzS_2WlxhGQpJ0zbMQE,53254
numpy/f2py/tests/__init__.py,sha256=46XgeBE0seimp3wD4Ox0KutYeLwdsdRSiGECcG1iYu8,328
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=JFU2w98cB_XNwfrqNtI0yDTmpEdxYO_UEl2pgI_rnt8,658
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=gvQJIzNtvacWE0dhysxn30-iUeI65Hpq7DiE9oRauz8,105
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=s6XLwujiCr6Xi8yBkvLPBXRmo2WsGVohU7K9ALnKUng,7478
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=But9r9m4iL7EGq_haMW8IiQ4VivH0TgUozxX4pPvdpE,29
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=oBwbGSlbr9MkFyhVO2aldjc01dr9GHrMrSiRQek8U64,460
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=rfzw3QdI-eaDSl-hslCgGpd5tHftJOVhXvb21Y9Gf6M,499
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=rmT9k4jP9Ru1PLcGqepw9Jc6P9XNXM0axY7o4hi9lUw,269
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=r08JeTVmTTExA-hYZ6HzaxVwBn1GMbPAuuwBhBDtJUk,130
numpy/f2py/tests/src/block_docstring/foo.f,sha256=y7lPCPu7_Fhs_Tf2hfdpDQo1bhtvNSKRaZAOpM_l3dg,97
numpy/f2py/tests/src/callback/foo.f,sha256=C1hjfpRCQWiOVVzIHqnsYcnLrqQcixrnHCn8hd9GhVk,1254
numpy/f2py/tests/src/callback/gh17797.f90,sha256=_Nrl0a2HgUbtymGU0twaJ--7rMa1Uco2A3swbWvHoMo,148
numpy/f2py/tests/src/callback/gh18335.f90,sha256=NraOyKIXyvv_Y-3xGnmTjtNjW2Znsnlk8AViI8zfovc,506
numpy/f2py/tests/src/callback/gh25211.f,sha256=a2sxlQhtDVbYn8KOKHUYqwc-aCFt7sDPSnJsXFG35uI,179
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=FWxo0JWQlw519BpZV8PoYeI_FZ_K6C-3Wk6gLrfBPlw,447
numpy/f2py/tests/src/callback/gh26681.f90,sha256=-cD69x7omk5wvVsfMHlXiZ-pTcaxs2Bl5G9GHA4UJ2M,566
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=5rvOfCv-wSosB354LC9pExJmMoSHnbGZGl_rtA2fogA,142
numpy/f2py/tests/src/cli/hi77.f,sha256=ttyI6vAP3qLnDqy82V04XmoqrXNM6uhMvvLri2p0dq0,71
numpy/f2py/tests/src/cli/hiworld.f90,sha256=QWOLPrTxYQu1yrEtyQMbM0fE9M2RmXe7c185KnD5x3o,51
numpy/f2py/tests/src/common/block.f,sha256=GQ0Pd-VMX3H3a-__f2SuosSdwNXHpBqoGnQDjf8aG9g,224
numpy/f2py/tests/src/common/gh19161.f90,sha256=BUejyhqpNVfHZHQ-QC7o7ZSo7lQ6YHyX08lSmQqs6YM,193
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=-5Din7YlY1TU7tUHD2p-_DSTxGBpDsWYNeT9WOwGhno,208
numpy/f2py/tests/src/crackfortran/common_with_division.f,sha256=2LfRa26JEB07_ti-WDmIveq991PxRlL_K6ss28rZDkk,494
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=ZSUAh3uhn9CCF-cYqK5TNmosBGPfsuHBIEfudgysun4,193
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=jYrJKZWF_59JF9EMOSALUjn0UupWvp1teuGpcL5s1Sc,197
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=19YO7OGj0IksyBlmMLZGRBQLjoE3erfkR4tFvhznvvE,693
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=hoyXw330VHh8duMVmAQZjr1lgLVF4zFCIuEaUIrupv0,175
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=CaH7mnWTG7FcnJe2vXN_0zDbMadw6NCqK-JJ2HmDjK8,128
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=jJly1AzF5L9VxbVQ0vr-sf4LaUo4eQzJguhuemFxnvg,375
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=7K5dtOXGuBDAENPNCt-tAGJqTfNKz5OsqVSk16_e7Es,340
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=qZHPRNQljIeYNwbqPLxREnOrSdVV14f3fnaHqB1M7c0,241
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=w3tr_KcY3s7oSWGDmjfMHv5h0RYVGUpyXquNdNFOJQg,126
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=41W6Ire-5wjJTTg6oAo7O1WZfd1Ug9vvNtNgHS5MhEU,101
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=1v-hMCT_K7prhhamoM20nMU9zILam84Hr-imck_dYYk,205
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=LWDJTYR3t9h1IsrKC8dVXZlBfWX7clLeU006X6Ow8oI,332
numpy/f2py/tests/src/crackfortran/gh27697.f90,sha256=bbnKpDsOuCWluoNodxzCspUQnu169zKTsn4fLTkhwpM,364
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=gPNasx98SIf7Z9ibk_DHiGKCvl7ERtsfoGXiFDT7FbM,282
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=-Fc-qjW1wBr3Dkvdd5dMTrt0hnjnV-1AYo-NFWcwFSo,1184
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=7bubZGMIn7iD31wDkjF1TlXCUM7naCIK69M9d0e3y-U,174
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=Pnwyf56Qd6W3FUH-ZMgnXEYkb7gn18ptNTdwmGan0Jo,167
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=eYpJwBYLKGOxVbKgEqfny1znib-b7uYhxcRXIf7uwXg,165
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=aINLh6GlfTwFewxvDoqnMqwuCNb4XAqi5Nj5vXguXYs,98
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=iUOtfHd3OuT1Rz2-yiSgt4uPKGvCt5AzQ1iygJt_yjg,82
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=iJCD8a8MUTmuPuedbcmxW54Nr4alYuLhksBe1sHS4K0,298
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=jcw-fzrFh0w5U66uJYfeUW4gv94L5MnWQ_NpsV9y0oI,998
numpy/f2py/tests/src/kind/foo.f90,sha256=zIHpw1KdkWbTzbXb73hPbCg4N2Htj3XL8DIwM7seXpo,347
numpy/f2py/tests/src/mixed/foo.f,sha256=90zmbSHloY1XQYcPb8B5d9bv9mCZx8Z8AMTtgDwJDz8,85
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=pxKuPzxF3Kn5khyFq9ayCsQiolxB3SaNtcWaK5j6Rv4,179
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=fIQ71wrBc00JUAVUj_r3QF9SdeNniBiMw6Ly7CGgPWU,139
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=9Uz8CHB9i3_mjC3cTOmkTgPAF5tWSwYacG3MUrU-SY0,180
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=WATiDGAoCKnGgMzm_iMgmfVU0UKOQlk5Fm0iXCmPAkE,179
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=c7VU4SbK3yWn-6wksP3tDx_Hxh5u_g8UnlDpjU_-tBg,402
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=eEU7RgFPh-TnNXEuJFdtJmTF-wPnpbHLQhG4fEeJnag,403
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=tDZ3fUlazLL8ThJm3VwNGJ75QIlLcW70NnMFv-JA4W0,224
numpy/f2py/tests/src/modules/use_modules.f90,sha256=UsFfx0B2gu_tS-H-BpLWed_yoMDl1kbydMIOz8fvXWA,398
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=fdOPhRi7ipygwYCXcda7p_dlrws5Hd2GlpF9EZ-qnck,157
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=KRg7Gmq_r3B7t3IEgRkP1FT8ve8AuUFWT0WcTlXoN5U,1468
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=-bBf2eqHb-uFxgo6Q7iAtVUUQzrGFqzhHDNaxwSICfQ,1939
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=re7pfzcuaquiOia53UT7qNNrTYu2euGKOF4IhoLmT6g,469
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=nEmMLitKoSAG7gBBEQLWumogN-KS3DBZOAZJWcSDnFw,612
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=IcxESVLKJUZ1k9uYKoSb8Hfm9-O_4rVnlkiUU2diy8Q,609
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=quNbDsM1Ts2rN4WtPO67S9Xi_8l2cXabWRO00CPQSSQ,610
numpy/f2py/tests/src/quoted_character/foo.f,sha256=WjC9D9171fe2f7rkUAZUvik9bkIf9adByfRGzh6V0cM,482
numpy/f2py/tests/src/regression/AB.inc,sha256=cSNxitwrjTKMiJzhY2AI5FaXJ5y9zDgA27x79jyoI6s,16
numpy/f2py/tests/src/regression/assignOnlyModule.f90,sha256=c9RvUP1pQ201O_zOXgV0xp_aJF_8llxuA8Uot9z5tr0,608
numpy/f2py/tests/src/regression/datonly.f90,sha256=9cVvl8zlAuGiqbSHMFzFn6aNWXj2v7sHJdd9A1Oc0qg,392
numpy/f2py/tests/src/regression/f77comments.f,sha256=bqTsmO8WuSLVFsViIV7Nj7wQbJoZ7IAA3d2tpRDKsnA,626
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=hcLZbdozMJ3V9pByVRp3RoeUvZgLMRLFctpZvxK2hTI,139
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=_W1fj0wXLqT91Q14qpBnM3F7rJKaiSR8upe0mR6_OIE,276
numpy/f2py/tests/src/regression/incfile.f90,sha256=i7Y1zgMXR9bSxnjeYWSDGeCfsS5jiyn7BLb-wbwjz2U,92
numpy/f2py/tests/src/regression/inout.f90,sha256=CpHpgMrf0bqA1W3Ozo3vInDz0RP904S7LkpdAH6ODck,277
numpy/f2py/tests/src/regression/lower_f2py_fortran.f90,sha256=CMQL5RWf9LKnnUDiS-IYa9xc9DGanCYraNq0vGmunOE,100
numpy/f2py/tests/src/return_character/foo77.f,sha256=WzDNF3d_hUDSSZjtxd3DtE-bSx1ilOMEviGyYHbcFgM,980
numpy/f2py/tests/src/return_character/foo90.f90,sha256=ULcETDEt7gXHRzmsMhPsGG4o3lGrcx-FEFaJsPGFKyA,1248
numpy/f2py/tests/src/return_complex/foo77.f,sha256=8ECRJkfX82oFvGWKbIrCvKjf5QQQClx4sSEvsbkB6A8,973
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=c1BnrtWwL2dkrTr7wvlEqNDg59SeNMo3gyJuGdRwcDw,1238
numpy/f2py/tests/src/return_integer/foo77.f,sha256=_8k1evlzBwvgZ047ofpdcbwKdF8Bm3eQ7VYl2Y8b5kA,1178
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=bzxbYtofivGRYH35Ang9ScnbNsVERN8-6ub5-eI-LGQ,1531
numpy/f2py/tests/src/return_logical/foo77.f,sha256=FxiF_X0HkyXHzJM2rLyTubZJu4JB-ObLnVqfZwAQFl8,1188
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=9KmCe7yJYpi4ftkKOM3BCDnPOdBPTbUNrKxY3p37O14,1531
numpy/f2py/tests/src/return_real/foo77.f,sha256=ZTrzb6oDrIDPlrVWP3Bmtkbz3ffHaaSQoXkfTGtCuFE,933
numpy/f2py/tests/src/return_real/foo90.f90,sha256=gZuH5lj2lG6gqHlH766KQ3J4-Ero-G4WpOOo2MG3ohU,1194
numpy/f2py/tests/src/routines/funcfortranname.f,sha256=oGPnHo0zL7kjFnuHw41mWUSXauoeRVPXnYXBb2qljio,123
numpy/f2py/tests/src/routines/funcfortranname.pyf,sha256=coD8AdLyPK4_cGvQJgE2WJW_jH8EAulZCsMeb-Q1gOk,440
numpy/f2py/tests/src/routines/subrout.f,sha256=RTexoH7RApv_mhu-RcVwyNiU-DXMTUP8LJAMSn2wQjk,90
numpy/f2py/tests/src/routines/subrout.pyf,sha256=c9qv4XtIh4wA9avdkDJuXNwojK-VBPldrNhxlh446Ic,322
numpy/f2py/tests/src/size/foo.f90,sha256=IlFAQazwBRr3zyT7v36-tV0-fXtB1d7WFp6S1JVMstg,815
numpy/f2py/tests/src/string/char.f90,sha256=ihr_BH9lY7eXcQpHHDQhFoKcbu7VMOX5QP2Tlr7xlaM,618
numpy/f2py/tests/src/string/fixed_string.f90,sha256=5n6IkuASFKgYICXY9foCVoqndfAY0AQZFEK8L8ARBGM,695
numpy/f2py/tests/src/string/gh24008.f,sha256=UA8Pr-_yplfOFmc6m4v9ryFQ8W9OulaglulefkFWD68,217
numpy/f2py/tests/src/string/gh24662.f90,sha256=-Tp9Kd1avvM7AIr8ZukFA9RVr-wusziAnE8AvG9QQI4,197
numpy/f2py/tests/src/string/gh25286.f90,sha256=2EpxvC-0_dA58MBfGQcLyHzpZgKcMf_W9c73C_Mqnok,304
numpy/f2py/tests/src/string/gh25286.pyf,sha256=GjgWKh1fHNdPGRiX5ek60i1XSeZsfFalydWqjISPVV8,381
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=6Y9zU66NfcGhTXlFOdFjCSMSwKXpq5ZfAe3FwpkAsm4,384
numpy/f2py/tests/src/string/scalar_string.f90,sha256=ACxV2i6iPDk-a6L_Bs4jryVKYJMEGUTitEIYTjbJes4,176
numpy/f2py/tests/src/string/string.f,sha256=shr3fLVZaa6SyUJFYIF1OZuhff8v5lCwsVNBU2B-3pk,248
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=JC0FfVXsnB2lZHb-nGbySnxv_9VHAyD0mKaLDowczFU,190
numpy/f2py/tests/test_abstract_interface.py,sha256=nGyPJgB0-d9Ttk3XsYb-N9HxfZxTVUz0gkl66u3JNaU,809
numpy/f2py/tests/test_array_from_pyobj.py,sha256=nXkuHwa0gvVOsyuKI2m1UfVu8HyKiFqBvIK23_zOdxw,23702
numpy/f2py/tests/test_assumed_shape.py,sha256=FeaqtrWyBf5uyArcmI0D2e_f763aSMpgU3QmdDXe-tA,1466
numpy/f2py/tests/test_block_docstring.py,sha256=2WGCsNBxtH57BjAYyPAzUZgiBRYWAQpC9zODP02OZec,582
numpy/f2py/tests/test_callback.py,sha256=8I31S55C4p3WXUFUY78fo8as-VpS6h7kNAPeUZrr7w0,7114
numpy/f2py/tests/test_character.py,sha256=zUsyZCO1FrhVxF-S_fuET_xjbWoJc3SrFCNY_buT7WU,21905
numpy/f2py/tests/test_common.py,sha256=VPsy0SLqbKaUGgDqesYXmjYuLpnPK-XyzseqmV5QnhM,641
numpy/f2py/tests/test_crackfortran.py,sha256=6Y_u1FJYpVkwE9615Bx24eMh67rtJEm1bIEegnpxvCg,16383
numpy/f2py/tests/test_data.py,sha256=SFYgovu5LBtIbS-zvbqkm9zoahHJx35LDOJoEqYP_kU,2888
numpy/f2py/tests/test_docs.py,sha256=GiQUqifxttwJRgkmLEoq5wIFjTlYLEAQ1n5Kw4Emsiw,1850
numpy/f2py/tests/test_f2cmap.py,sha256=-WnN0HlqiG9RPgc1P_KSLZvqgQ4wGYDf0lFcyfWOLfs,385
numpy/f2py/tests/test_f2py2e.py,sha256=K_883X2rw88Fn5a7bZPI03NFA3YD95NYopX0OHxcZAM,27868
numpy/f2py/tests/test_isoc.py,sha256=kY7yg7Jtyn_RBlozwe6UpQvtwPbPcpTC0B27s2GRo7s,1428
numpy/f2py/tests/test_kind.py,sha256=myLQNDPZDdVq7PNjXWUgkY3M-JdzP5MJNZ1PE_ChNEI,1783
numpy/f2py/tests/test_mixed.py,sha256=iMMRt1q7woHuKSfqiw4LsaU9wIRq2FnvT0lv74fR7V0,860
numpy/f2py/tests/test_modules.py,sha256=wli_Cq9FroWg9nnOZplGAd9L5OX49h_Z-e8PyVVnk0w,2299
numpy/f2py/tests/test_parameter.py,sha256=j4sNNiHkj-jbl3FC4v_tnksgpydbHqNvNI2tzlVFGYE,4623
numpy/f2py/tests/test_pyf_src.py,sha256=eD0bZu_GWfoCq--wWqEKRf-F2h5AwoTyO6GMA9wJPr4,1135
numpy/f2py/tests/test_quoted_character.py,sha256=T6I2EyopdItKamcokG0ylvhT7krZYhBU6hF3UFIBr2g,476
numpy/f2py/tests/test_regression.py,sha256=L95aSnN9lOVRkmGPVRaVF4w6hJ3iHgQ8BPM34Uef35I,5849
numpy/f2py/tests/test_return_character.py,sha256=DP63vrF6bIV-QRBsJ1ZpPsKz-u906Ph8M6_biPEzBJs,1511
numpy/f2py/tests/test_return_complex.py,sha256=4vtpIYqAZZrbKYi3fnP7l_Zn42YnBbPwl8-eNfZOHHo,2415
numpy/f2py/tests/test_return_integer.py,sha256=qR8Ismf40Ml2impqjGzjL2i-CRyGTxXVEvzQQMkJfJo,1776
numpy/f2py/tests/test_return_logical.py,sha256=XCmp8E8I6BOeNYF59HjSFAdv1hM9WaDvl8UDS10_05o,2017
numpy/f2py/tests/test_return_real.py,sha256=KMIRQP9xjz09-wFX-jeMbkNQPXegnfd-Qhc4W4qKHeA,3247
numpy/f2py/tests/test_routines.py,sha256=TflyDvptl5dREgZFv6hlauRvsK_FFUo7ZTVsiIYPcio,794
numpy/f2py/tests/test_semicolon_split.py,sha256=6aGgOdtGpJSgPZlzpow-tcHXSPqrJeKagWnFilheWeM,1626
numpy/f2py/tests/test_size.py,sha256=CsElZF4N5Tf7fr27TJudu3JD_JKb63SubUXPYjl5Llg,1154
numpy/f2py/tests/test_string.py,sha256=wfV6jxkOnoJWOM7i5Ee7gc2nXK_Gyb3FqNI4wLfVQhk,2936
numpy/f2py/tests/test_symbolic.py,sha256=28quk2kTKfWhKe56n4vINJ8G9weKBfc7HysMlE9J3_g,18341
numpy/f2py/tests/test_value_attrspec.py,sha256=jYtbvVyg8uOZsdcCeLhaXIdR7MOfMh1j04aXbJNbfK8,329
numpy/f2py/tests/util.py,sha256=WKEixdQq0xJV3Hg60a-6xc1T5GhKDngfPEw-WNfoqjg,12174
numpy/f2py/use_rules.py,sha256=oMjkw5fP55MhGAqdDcO_dknbQBE9qLljU7y6-HDoerY,3515
numpy/fft/__init__.py,sha256=cW8oJRorHlG10mhnhAB1OOkg4HpG2NGYHDgonFNI04s,8326
numpy/fft/__init__.pyi,sha256=KvQQpPxk9LKgqMIB3AGJCsQDu3ownGKjjT7McQKNpXY,514
numpy/fft/_helper.py,sha256=Yvph-5gksd0HebLSXq4UKfVYOwSiqNIa4THpv0aA2HE,6775
numpy/fft/_helper.pyi,sha256=dek8ibnRL8Y2erBdDt7ydWyAVXLb46SPTctLy_TEKoE,1341
numpy/fft/_pocketfft.py,sha256=Q6J5inX10oPBtX-lblPlYExuzycovGr-LFMT7QYe9pc,62692
numpy/fft/_pocketfft.pyi,sha256=Dvhdy8Y2R1HmTu-99z4Pgd4WCnC6eg3OVzUY4yOQpTo,3155
numpy/fft/_pocketfft_umath.cpython-312-x86_64-linux-gnu.so,sha256=GMq6MpylYoJQSHfdBdLT9pWskt6wRMQG2xzFD3UJGJQ,649272
numpy/fft/helper.py,sha256=str0NJ1vpLNlC_3vMfulTu9D9_cThxKG2zkaGuZ5NTY,610
numpy/fft/helper.pyi,sha256=KsF45bVyZ4_eJbBFpkER9L8MCWmg7dJuhLqY_7uFNZs,891
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/test_helper.py,sha256=pVYVLUwNEcE9M8eyHaRi7JOgc6k5p_JVzJ0AKnelgvI,6149
numpy/fft/tests/test_pocketfft.py,sha256=euC7OA8_h_EQ0aO_UqBNPARx3xb2LgJS-rsWe3XiE-U,24410
numpy/lib/__init__.py,sha256=IvUoSO27nHWmaTCs4fqJLDIWIcaj-uRIbR9YfkyptAo,3226
numpy/lib/__init__.pyi,sha256=nevfu40fu_qSozt-vdcUGh_zQijGGWxfdns2x9_LsWI,518
numpy/lib/_array_utils_impl.py,sha256=eMGdZi7auu6201h4v4eQZ2miF8KmdMGDApbBFgRE-6Q,1689
numpy/lib/_array_utils_impl.pyi,sha256=2OjfMvbUUlTrJHvGHIcnlrHxPWl7LcpFo3gmuVz6nWg,793
numpy/lib/_arraypad_impl.py,sha256=xm8Pkunt7DAKAWvnySEsoWbjMvcmZL-OC7qe-LQaruQ,32326
numpy/lib/_arraypad_impl.pyi,sha256=G9GSX6q0glWgoPN5FGzO-Ql1UdkKadoyfVy1B0HH9iM,1792
numpy/lib/_arraysetops_impl.py,sha256=CBDoG2fWzx0OITbZEKlFD5WUo9HQRqmsweoMOtHKQjs,39309
numpy/lib/_arraysetops_impl.pyi,sha256=4BeISAFRLNdag94QdBWWJTOWr2RPaWcNkkiCBuIdejU,9569
numpy/lib/_arrayterator_impl.py,sha256=qx6gqxLTNY4Lea6Lh6J-Cud4RyLIqnELhi-kMwK8vKU,7186
numpy/lib/_arrayterator_impl.pyi,sha256=8ozo2UTKx-9fIDXhVMYDyhlfuMa-uPlWCp25TRk61kE,1827
numpy/lib/_datasource.py,sha256=FJ7k1HghREU7udh8ZuO5ZZF3nHJfOkj7iWijhoVFqIQ,22729
numpy/lib/_datasource.pyi,sha256=135RvD3p-3mHdNp_sZV4aN9brwEFvEM49VE1eHlFEfs,996
numpy/lib/_function_base_impl.py,sha256=eeZaizFpsCLeisLcxrQ-eK4R-8enw6YKPV1jeDPwCzU,196038
numpy/lib/_function_base_impl.pyi,sha256=Ce0vkW7qM9i4G2Br-X8A6V9XwPZ5cKiIB030zlSUFvQ,22228
numpy/lib/_histograms_impl.py,sha256=Lw_9LfM_Z7qBef3boamH5LtL7qiT10gpIyWy9Uj6lTo,38762
numpy/lib/_histograms_impl.pyi,sha256=7B4b29m97PW5GDSgOKi_3Ul-XyWbo6NjMW264FFxjSI,1070
numpy/lib/_index_tricks_impl.py,sha256=12iGjjak3hiMfwnh5zR2JqA78-or-u9P1gTGCcjJD0E,32179
numpy/lib/_index_tricks_impl.pyi,sha256=WOKkVvojes2D2Uc8itHkjv6fZF1VqwocnqVu05aiCIs,6325
numpy/lib/_iotools.py,sha256=mMhxeGBt-T8prjWpNhn_xvZCj6u6OWWmmsvKP6vbM5w,30941
numpy/lib/_iotools.pyi,sha256=4AQxPlLCoIruq04RAa-xtC8swL1ChuLT9SqhQAfeflQ,3387
numpy/lib/_nanfunctions_impl.py,sha256=gX6NUKgCQKvuFTSAObhqfrqQIXIsxnKIQOc-heOn7rs,72150
numpy/lib/_nanfunctions_impl.pyi,sha256=o0ILqctzjyHwNJ3zs4bdd8qJ9qVtyGfL6FChCf4IPGg,833
numpy/lib/_npyio_impl.py,sha256=UA84bpOF9xfELBsagASjHf1E5GgLWvs_Y_Gmtplkovw,99377
numpy/lib/_npyio_impl.pyi,sha256=byvWXIh9qHzEQGvHKkclD9_oBGn8y6JkU5aUhSjhZyo,9270
numpy/lib/_polynomial_impl.py,sha256=6rD5Cy4mSDk2CsuAdJOq2he-PSa-ZiqsdgyyQAF5qx0,44294
numpy/lib/_polynomial_impl.pyi,sha256=NoMMI6aJmcnLKQyaM3B2hSJTFJtx7mAqEHPsCC_rM7s,7117
numpy/lib/_scimath_impl.py,sha256=dUxb9XD-AJPboK_LO3LA0KgykFSUEOG5BVGrhwm2Qqo,15691
numpy/lib/_scimath_impl.pyi,sha256=Xdyj3nbEBEE5p6K_ZIjilsAgvaxoGK8TEoV2vdzpLIE,2955
numpy/lib/_shape_base_impl.py,sha256=AHbXPp4sH0gEJgSyM0A9zgmM9Mwm6jR_p5pWtUXeqV8,39353
numpy/lib/_shape_base_impl.pyi,sha256=RysQQNQ6fbI_IyavO9AXPMynIGD7vf6I0_dc5_4wUpI,5288
numpy/lib/_stride_tricks_impl.py,sha256=y3Uxp3jFzDwmIQ137N2zap7-vW_jONUQmXnbfqrs60A,18025
numpy/lib/_stride_tricks_impl.pyi,sha256=ZX9Dp4oLmi-FSwY8o4FSisswTgfE5xwlTCjk2QkbIG8,1801
numpy/lib/_twodim_base_impl.py,sha256=r31aBnzCSBpq_em4HyLiSMeTiRzlHAn7Bd4yXYqyEFY,33864
numpy/lib/_twodim_base_impl.pyi,sha256=30vYsjEVCOaJ8NDgk4KKpvkW-C4_hV-NbJAd3VswVXI,11269
numpy/lib/_type_check_impl.py,sha256=Dv9a7QCR1bqBHoXgCjmPrGEewG1v2BBtE_8VfcF4ySU,19220
numpy/lib/_type_check_impl.pyi,sha256=XuaIBCJI1z50pcH6ScB3oMAyBjAxX_LY4KU0gUZaTAM,5165
numpy/lib/_ufunclike_impl.py,sha256=0eemf_EYlLmSa4inNr3iuJ1eoTMqLyIR0n6dQymga3Y,6309
numpy/lib/_ufunclike_impl.pyi,sha256=Tle2e2qLfaYNlECFw6AVgazMnAHYCE9WO96ddZiM1dw,1322
numpy/lib/_user_array_impl.py,sha256=pqmz3qNx620zngeIFmSg8IiXNdTMVBAglt81hEJNh5Y,7971
numpy/lib/_user_array_impl.pyi,sha256=Zfknkdua_dgoO9U7rDXHYzuachGOVFeLu1X0760dvR8,9301
numpy/lib/_utils_impl.py,sha256=4xYQczoX7i_wHjugnl0ba1VExSbV48ndVow08S8G0WQ,23388
numpy/lib/_utils_impl.pyi,sha256=3UJqa7IVH6QVJbQfKAqblyHxjPfaCAR28KmDxXeIpU0,277
numpy/lib/_version.py,sha256=nyRagTCuE69-0P9JTIcKK7jbzRGbsgnqVtFIrNzTFsM,4854
numpy/lib/_version.pyi,sha256=vysY5Vl_nh4si6GkMXEoB6pUDl-jJ5g0LpSDa40F124,641
numpy/lib/array_utils.py,sha256=zoaLw9TvrAFRkh9n8uMyr8kvug3IvVlUT7LcJzB3Tk0,130
numpy/lib/array_utils.pyi,sha256=kEO5wShp8zEbNTPu-Kw-EHuZQvq1rXHzgjK797xCV0Q,191
numpy/lib/format.py,sha256=XMMQzYOvc8LgeNpxX7Qpfurli2bG5o9jAaeY55tP85A,36200
numpy/lib/format.pyi,sha256=cVuydIbVhG_tM7TrxEVBiERRPLOxKS8hLCTOT7ovtzc,748
numpy/lib/introspect.py,sha256=SiQ5OwgvE-1RoQOv2r__WObS5QEUBohanyCd7Xe80UU,2715
numpy/lib/introspect.pyi,sha256=AWVX6b9mzdwsxizOY0LydWKBEpGatHaeeXGc2txYJEM,152
numpy/lib/mixins.py,sha256=_yb3iwwzUfSbN7HpJSp3FhFkgV3WViTHS5SAHkK8Lmc,7337
numpy/lib/mixins.pyi,sha256=q_lxMe-PpNlvpEJ--nLkyi0qVD0QuNHriF3XHfxyJok,3131
numpy/lib/npyio.py,sha256=NCxqWedJbSM5M-wr69TED8x7KXcyBJ0x5u49vj4sPkI,62
numpy/lib/npyio.pyi,sha256=MTP8KyQ2GTU8BTkpaMHnwDQO9ABrHRiCCEO5BfQGgLo,116
numpy/lib/recfunctions.py,sha256=5fbg0aMuDbgOpfYmDTByhlNKZWgNkCVDdA8BQ4zZXzA,59654
numpy/lib/recfunctions.pyi,sha256=Ri9FikspmLm_67f86udizNNMZzaOXz1nulRNcxfYDWc,13283
numpy/lib/scimath.py,sha256=iO0IiDgpHk1EurdUvJIE2KqDzVOfvSsU3MFIlJskIOE,118
numpy/lib/scimath.pyi,sha256=UND4g92K5-6_I0YWqu7qbDTHU_sePpa0I58MTMH0yhA,233
numpy/lib/stride_tricks.py,sha256=VGR5M8Jyw8IC4S6XEB9NN_GULTJJQj_1QrItIi_BJiM,82
numpy/lib/stride_tricks.pyi,sha256=Fqn9EZXdjIgUTce6UMD7rBBb8289QTMzohhjHwYP3TU,124
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=65KXfUUvp8wXSqgQisuYlkhg-qHjBV5FXYetL8Ba-rc,10571
numpy/lib/tests/test__iotools.py,sha256=W2gLNsi2S8-4qixUs6EKkTYnOOp55qLLuM3zpBzZoR4,13744
numpy/lib/tests/test__version.py,sha256=aO3YgkAohLsLzCNQ7vjIwdpFUMz0cPLbcuuxIkjuN74,1999
numpy/lib/tests/test_array_utils.py,sha256=vOC6AmlPIQbVxQf2DiRL02May5IK5BK2GUFK0nP83FM,1119
numpy/lib/tests/test_arraypad.py,sha256=PuDd3s7w_r54B2ILXKQheFMUxklylHPN-vHVq_mGjk8,56064
numpy/lib/tests/test_arraysetops.py,sha256=Y5sS11K5r4KGcPaZHxOaoBUJDZAjcfdF7qSvxpVydqo,38023
numpy/lib/tests/test_arrayterator.py,sha256=AYs2SwV5ankgwnvKI9RSO1jZck118nu3SyZ4ngzZNso,1291
numpy/lib/tests/test_format.py,sha256=pbIhtX3E07MU9GN30frL--KKMW3iYm1eFaTiwPq39MU,40911
numpy/lib/tests/test_function_base.py,sha256=YgxaRTjpKNzw92xkMxdxY2JwHQoBOryb-06gaRiTn1c,168840
numpy/lib/tests/test_histograms.py,sha256=pSUHeO9nY5Gf5VXyCCZ9qoRjrXT1Y4c0xRcz5FeAPCY,33694
numpy/lib/tests/test_index_tricks.py,sha256=ZpKsvd3P3p2hwfj6sHlL_lysJp1IevAoM6AdpeTAx8M,20368
numpy/lib/tests/test_io.py,sha256=1brG0DanJdQhK680J-zR4YlBc-oDfv_QUYkIOb8oPzQ,110047
numpy/lib/tests/test_loadtxt.py,sha256=uP0SIRUpBGS4rZR7iXtwDsf1vpCZk_1f29NvYcci738,40522
numpy/lib/tests/test_mixins.py,sha256=Wivwz3XBWsEozGzrzsyyvL3qAuE14t1BHk2LPm9Z9Zc,7030
numpy/lib/tests/test_nanfunctions.py,sha256=iN7Lyl0FlDjlE23duS6YS_iEoWRSPP8tydQLdmSMWsI,53344
numpy/lib/tests/test_packbits.py,sha256=2QaNYKH29cVD-S4YYBIQBd1xQ9bc2OqHdZT6yS7Txjk,17544
numpy/lib/tests/test_polynomial.py,sha256=1gJhzbXglqeGMjo8OnpP4EASiCVvkYPiNOHKirAlNfg,11428
numpy/lib/tests/test_recfunctions.py,sha256=KHHrlYhCrVVZh4N4e8UMac8oK4aX438Na1AchTdJsxU,43987
numpy/lib/tests/test_regression.py,sha256=YdZ_xYXzFh3WFyAKF5lN7oFl5HMm5r38C1Lij3J8NuQ,7694
numpy/lib/tests/test_shape_base.py,sha256=W1q-tgBENS19wpOKSzEi63OSjatE4qC1viQG22qoacE,27488
numpy/lib/tests/test_stride_tricks.py,sha256=9g25TXSGLsvfeIrlkQ8l1fx_pZ48b4dxCzXXUbsKC5g,22997
numpy/lib/tests/test_twodim_base.py,sha256=ll-72RhqCItIPB97nOWhH7H292h4nVIX_w1toKTPMUg,18841
numpy/lib/tests/test_type_check.py,sha256=9ycqRSw0TzrJfu4gknQYblRPEsWlMI9TWPP_jyI8w-c,14680
numpy/lib/tests/test_ufunclike.py,sha256=5AFySuvUfggh0tpBuQHJ7iZRrP0r_yZZv5xHxOuCZ1s,3023
numpy/lib/tests/test_utils.py,sha256=zzgwQGId2P8RUgimSsm7uMCYb61xPenrP_N0kcZU8x4,2374
numpy/lib/user_array.py,sha256=Ev3yeNNLZVNWk9xZuiCIbODYKwQ6XfYGpI5WAoYvtok,49
numpy/lib/user_array.pyi,sha256=8C-aTekEYA0bVU7F3turaw1w0j8FfFvDp9xKa9Pfe94,53
numpy/linalg/__init__.py,sha256=XNtdLo33SVTjQbXeimLFa5ZudzpEEwnfJBNorVbxuyc,2106
numpy/linalg/__init__.pyi,sha256=o8K7PS_GETdEtnE7uXgJV7wnR8B0hH79AKpsmBHbJhA,1006
numpy/linalg/_linalg.py,sha256=QNeVUH1DXQe7X5Ygp-LV9W1tN7sbwbhMXIQbRNPYJX0,114680
numpy/linalg/_linalg.pyi,sha256=sCY_eH3ygLI05xCQ278LcS5XofiOPB-G_-cYY1Q2FTA,11385
numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so,sha256=zCbwZ-NcmSSPNUZS-3qUH9bGCCO7EAUk0kX2pU-866Y,227657
numpy/linalg/_umath_linalg.pyi,sha256=awvRP1FGuomyfeaR0wzHvrXURAI8tUF3u2RRZ24hkXw,1409
numpy/linalg/lapack_lite.cpython-312-x86_64-linux-gnu.so,sha256=ALD4g2NjZ-Ryl6eQJKxRfU-VkARDRHar_C9IOh5-aQw,30009
numpy/linalg/lapack_lite.pyi,sha256=9HbrKm6Xc3jdXwjNcIm26mvm7M_sT8aug5p-e6lyw2c,2677
numpy/linalg/linalg.py,sha256=JQWcEvjY_bjhaMHXY5vDk69OIoMzX5Rvbn1eGW2FCvE,584
numpy/linalg/linalg.pyi,sha256=8E5sbKeM5Ors7r143mM7A4ui8kFZM0SF7NfUGW1eN-4,932
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/test_deprecations.py,sha256=9p_SRmtxj2zc1doY9Ie3dyy5JzWy-tCQWFoajcAJUmM,640
numpy/linalg/tests/test_linalg.py,sha256=DhZiFKqO7SVhtNwRqe5jbhcACbqttIiJfruY5rbLj-Q,83315
numpy/linalg/tests/test_regression.py,sha256=RMl5Jq-fLVDUSMnEmpP2-gigM5dzUfzURywa1tMK8CA,6689
numpy/ma/API_CHANGES.txt,sha256=F_4jW8X5cYBbzpcwteymkonTmvzgKKY2kGrHF1AtnrI,3405
numpy/ma/LICENSE,sha256=BfO4g1GYjs-tEKvpLAxQ5YdcZFLVAJoAhMwpFVH_zKY,1593
numpy/ma/README.rst,sha256=krf2cvVK_zNQf1d3yVYwg0uDHzTiR4vHbr91zwaAyoI,9874
numpy/ma/__init__.py,sha256=iv-YxXUZe4z7W53QZWY0ndicV43AGsIygArsoN3tQb8,1419
numpy/ma/__init__.pyi,sha256=H7zEUcvlhWQkYpoOQ9UyrLOuz23vnd_GYO_JiztGG04,6946
numpy/ma/core.py,sha256=jN3Z0xIb8a3lBAOAcUhGn8YlK-Ko5qm-1XadzBqAp1k,290518
numpy/ma/core.pyi,sha256=QGAzV8TDIhaffgX-OPFUhnWYCwzwmZs9OOYyGPLhR9U,18179
numpy/ma/extras.py,sha256=ZbseZmOKCD1f5w8NZP864TtkOWTw5c5KzzPNqmZFeR4,70630
numpy/ma/extras.pyi,sha256=J8HZzQWyNC1Uf-PV7QzfaQuJ9vyO_A2RwIfro0S9T7s,3804
numpy/ma/mrecords.py,sha256=7xEqcIH6iY8AT0ApnCCfrJvr17boJrgl9loqgbRuhso,27114
numpy/ma/mrecords.pyi,sha256=xHMSbdNKOeXtZP73NUA7aVmGs9F7sTiqAcYJ1o7QNMA,1983
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/test_arrayobject.py,sha256=MSvEcxlsVt4YZ7mVXU8q_hkwM0I7xsxWejEqnUQx6hE,1099
numpy/ma/tests/test_core.py,sha256=KufMvdrDZ8FIgTQW6UWl4M1WzDY32y1rYHwyJbvn13g,219264
numpy/ma/tests/test_deprecations.py,sha256=nq_wFVt2EBHcT3AHxattfKXx2JDf1K5D-QBzUU0_15A,2566
numpy/ma/tests/test_extras.py,sha256=h0Zc0u4dXlQ3E0qADNYlH7iF4XX3K2A6HiY5hseRwSs,78314
numpy/ma/tests/test_mrecords.py,sha256=-nFjKUNYG_-gJ6RpZbWnx_TJlmkRAagA7AnVaf9YJfI,19855
numpy/ma/tests/test_old_ma.py,sha256=BW01_4m8wZcHvAkZ8FIjDmFfusnjgFmGVbRyqbWD000,32753
numpy/ma/tests/test_regression.py,sha256=foMpI0luAvwkkRpAfPDV_810h1URISXDZhmaNhxb50k,3287
numpy/ma/tests/test_subclassing.py,sha256=p5N5b5LY1J0pwDCbju0Qt28wZ1Dd2OfZ1dR4tphiFFY,17009
numpy/ma/testutils.py,sha256=sbiHivmwPQX3fPAPUe9OMktEqrwg1rcr8xgKfMM1Ex0,10272
numpy/ma/timer_comparison.py,sha256=FC9KhuSVUdyDP-YQUDQXKhUmrTzC8zsOIBrarMISrc4,15711
numpy/matlib.py,sha256=_SLwSvwuHVy4nzc2lFd49OqK1m6aWPX1YyKgzyW3A-E,10657
numpy/matlib.pyi,sha256=jochXdHIBmB5qMHiMVarjfdFUyu7AcRucxVrf2UoGpA,9628
numpy/matrixlib/__init__.py,sha256=BHBpQKoQv4EjT0UpWBA-Ck4L5OsMqTI2IuY24p-ucXk,242
numpy/matrixlib/__init__.pyi,sha256=hoxSBzgGaB2axvVIKt8wMefSseGWKDjFg3nAx-ZjNoU,105
numpy/matrixlib/defmatrix.py,sha256=BGV3oVcQ98-gzqMs3WNC0-x76fmfaGS_2bDnLBHPh90,30800
numpy/matrixlib/defmatrix.pyi,sha256=ijXIceS3SMbjt_fEn8qCUX_KllbJqDWIo4x6aDKLoqg,478
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/test_defmatrix.py,sha256=tLHvsnn2xIKLLZULYqhQ1IJOtSdS52BfOOhU8-7jjvA,15035
numpy/matrixlib/tests/test_interaction.py,sha256=jiLmXS0JtwEx0smkb5hUnY5Slp9I8FwGlYGHKE3iG1w,11895
numpy/matrixlib/tests/test_masked_matrix.py,sha256=1x3mzFol1GYvVxKXcmRYLi-On3cmK7gEjSVEyvbkh-w,8914
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=ObbSUXU4R2pWajH__xAdizADrU2kBKDDCxkDV-oVBXc,2059
numpy/matrixlib/tests/test_multiarray.py,sha256=jB3XCBmAtcqf-Wb9PwBW6uIykPpMPthuXLJ0giTKzZE,554
numpy/matrixlib/tests/test_numeric.py,sha256=MP70qUwgshTtThKZaZDp7_6U-Z66NIV1geVhasGXejQ,441
numpy/matrixlib/tests/test_regression.py,sha256=LBkm6_moDjuU9RY4FszgaknOj3IyCp3t-Ej3HJfqpdk,932
numpy/polynomial/__init__.py,sha256=XNK7ZWsBECCoHnJZ0NqKiF1ErZqvdxszE1NJ6Hc2Vz0,6760
numpy/polynomial/__init__.pyi,sha256=6NI7z3v8xTwVp3MBMxi_9W0-IZplayxzdx8BWaqymuI,687
numpy/polynomial/_polybase.py,sha256=Nhq-h1fKS_ARFPd6BRqya1gROmqA0KX1_eGON5AyYsw,39451
numpy/polynomial/_polybase.pyi,sha256=fZLj1aw9-tRf0yQSAXHjETPGgrAeqW9v36nlhDNDeyc,8534
numpy/polynomial/_polytypes.pyi,sha256=zqNdSGV9EIKoVcZSugAb3sDgFXj99m70Yngkt3jVPW8,22567
numpy/polynomial/chebyshev.py,sha256=U8Pl0r9l3AV96xISmaDjb-bvbCVT61rm7zWiT5L8_wg,62165
numpy/polynomial/chebyshev.pyi,sha256=9cJoCeRvzHuunQoCEy2pGOUdCp0KU65q7Tb8pTqLvGU,4725
numpy/polynomial/hermite.py,sha256=p1bX18L-fUwWFtmu0J4FnahBB9cWLCsUWLkXItQ7zB0,54466
numpy/polynomial/hermite.pyi,sha256=dm1gYq04GxQu5T4N5LqTYbZblLoXDqZDs6CtmycCU3w,2445
numpy/polynomial/hermite_e.py,sha256=ce0POlSbqQTqvkcXLIn7v7GqtmEaxc3J1xmaaD8VEfw,52208
numpy/polynomial/hermite_e.pyi,sha256=klpXixSq5MRTlh6AlN1jRXPDXcnRdgUZPTxQjZpFKhM,2537
numpy/polynomial/laguerre.py,sha256=dzeRDPs1lvJyVz6XeLu_ynPDF4SEFGjpIhLdmIMkf94,52379
numpy/polynomial/laguerre.pyi,sha256=QiCFjYZRAuYaty8LelfOvomgal1xFU9-4oKL68l1jyc,2174
numpy/polynomial/legendre.py,sha256=xoXBoVGToSllDsWHU3nBQJSBQLhJZBhMpA_bemYXDHQ,50994
numpy/polynomial/legendre.pyi,sha256=SaQ9PZG50KF4g0iQd6B-xYOBz1vTDGtI4wChAINlFZY,2173
numpy/polynomial/polynomial.py,sha256=lto2jYRcSVM3_PuKm3rbbYkHp4eMbOVX3VSO6rHmBrc,52202
numpy/polynomial/polynomial.pyi,sha256=Y4yeYfi879s5_Xm3SqdRmhQhbgJJBRRbajhCj1irTSw,2002
numpy/polynomial/polyutils.py,sha256=gF4_BiLkY8ySFlzawPVxr2Zcnoos3SMRn2dpsB0yP4c,22530
numpy/polynomial/polyutils.pyi,sha256=XYAYqUmjZVS_49uDszZE3SNI_lxJgx1SkjqqBVDrz44,10426
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/test_chebyshev.py,sha256=6tMsFP1h7K8Zf72mNOta6Tv52_fVTlXknseuffj080c,20522
numpy/polynomial/tests/test_classes.py,sha256=Tf6p3qCINxOfh7hsOdVp81-CJPkqNg1HnH2smcWbRBw,18450
numpy/polynomial/tests/test_hermite.py,sha256=0iUoYpgXiLrqm_dWD45Cs1PFJ8fHADFtlBN4TkLNNQw,18576
numpy/polynomial/tests/test_hermite_e.py,sha256=_A3ohAWS4HXrQG06S8L47dImdZGTwYosCXnoyw7L45o,18911
numpy/polynomial/tests/test_laguerre.py,sha256=5ku3xe4Gv5-eAGhyqwKj460mqoHvM5r_qsGu6P8J0es,17510
numpy/polynomial/tests/test_legendre.py,sha256=4AXrwrxCQoQ5cIMlYJpHJnAiaikLfvlL-T5TY7z9mzo,18672
numpy/polynomial/tests/test_polynomial.py,sha256=bkIpTFGh3ypMAZCulWYw6ZPFpqrlbbSAoivrIwBQAtw,22013
numpy/polynomial/tests/test_polyutils.py,sha256=ULZMU2soHOZ4uO0eJoRjxNkT3yGURuX35MXx1Bg5Wyk,3772
numpy/polynomial/tests/test_printing.py,sha256=99Qi6N880A3iyRZG5_AsZkDAKkFCUKgOZCp9ZhNMrOQ,21302
numpy/polynomial/tests/test_symbol.py,sha256=Hg-V7jR7qz5FKg_DrlkaiFcCI1UujYFUJfpf2TuoJZM,5372
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=EDFmtiuARDr7nrNIjgUuoGvgz_VmuQjxmeVh_eSa8Z8,3511
numpy/random/__init__.pxd,sha256=9JbnX540aJNSothGs-7e23ozhilG6U8tINOUEp08M_k,431
numpy/random/__init__.py,sha256=81Thnexg5umN5WZwD5TRyzNc2Yp-d14B6UC7NBgVKh8,7506
numpy/random/__init__.pyi,sha256=ETVwiw_jFxeouKFkzq0ociR0bJgLz3L3OBixxBv9Jho,2158
numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so,sha256=Js97deHVbVVXXxEdohlCo_JMBOBE0NS-U3RjEuFo-BU,319560
numpy/random/_bounded_integers.pxd,sha256=SH_FwJDigFEInhdliSaNH2H2ZIZoX02xYhNQA81g2-g,1678
numpy/random/_common.cpython-312-x86_64-linux-gnu.so,sha256=OtmxiQuPKn8_jeDC1SwXnd8zO3oabVGXLMQHdIyQAzk,238304
numpy/random/_common.pxd,sha256=7kGArYkBcemrxJcSttwvtDGbimLszdQnZdNvPMgN5xQ,4982
numpy/random/_examples/cffi/extending.py,sha256=xSla3zWqxi6Hj48EvnYfD3WHfE189VvC4XsKu4_T_Iw,880
numpy/random/_examples/cffi/parse.py,sha256=Z69FYSY6QQnZAJdIVlE-I2JAkEutRbdvZDXlm633Ynk,1751
numpy/random/_examples/cython/extending.pyx,sha256=ePnHDNfMQcTUzAqgFiEqrTFr9BoDmbqgjxzrDLvV8fE,2267
numpy/random/_examples/cython/extending_distributions.pyx,sha256=YCgFXHb7esnir-QmoAlde4y91FYuRMT94UNg9yb-Y4A,3847
numpy/random/_examples/cython/meson.build,sha256=GxZZT_Lu3nZsgcqo_7sTR_IdMJaHA1fxyjwrQTcodPs,1694
numpy/random/_examples/numba/extending.py,sha256=Ipyzel_h5iU_DMJ_vnXUgQC38uMDMn7adUpWSeEQLFE,1957
numpy/random/_examples/numba/extending_distributions.py,sha256=M3Rt9RKupwEq71JjxpQFbUO7WKSOuLfR1skRM2a-hbI,2036
numpy/random/_generator.cpython-312-x86_64-linux-gnu.so,sha256=FusC8uvFtNHibe36GLZa7enyqKCVoH080sJXAIol0kw,988680
numpy/random/_generator.pyi,sha256=YrqaEq8SfCo-C2EvuMDL9Kg3n1YZPSzF_1EshkuB3Ec,24009
numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so,sha256=rO-_BMIuS2dLnAwqx5LkvhIB_26gEOsbUVSyW6U90cY,137616
numpy/random/_mt19937.pyi,sha256=nX9OPiLcGFXn5cIE9k1TpvmVB0UBi9rlTsvGW5GP-Z0,775
numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so,sha256=9dmkFkNE0RgEBN5tu30JUed7WyQ36lCJqjry0dOCBsk,147744
numpy/random/_pcg64.pyi,sha256=gljmVLjVlgAMWGzQa6pzlzNW5H8kBvgDseQfIQcjy3k,1142
numpy/random/_philox.cpython-312-x86_64-linux-gnu.so,sha256=sR4XaLodjJbeEhPx742x3MrJXmmGbt5RH6jdBD0_xJU,120360
numpy/random/_philox.pyi,sha256=xf8EUX7Wa7-tYSU0LntUxMDVrNVcmjgACbubrb0O5sI,1005
numpy/random/_pickle.py,sha256=4iS9ofvvuD0KKMtRpZEdBslH79blhK8wtjqxeWN_gcE,2743
numpy/random/_pickle.pyi,sha256=Qdd9MkruVUeduANTkweO8dLNbeYegtOLVgnF6j0lRQE,1608
numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so,sha256=ZPEvMIvqElLyhAVq8tmOeBlCtcUImwW5WJZqv2h84WM,89456
numpy/random/_sfc64.pyi,sha256=gdDHDFsH-o-OB6zKJJqj8vNYvRm0GMXHApikapFvv50,682
numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so,sha256=5YAxWizYgDyQ-H7WPZmcDTLHpPIYgfK1_MoTqVn0niw,234720
numpy/random/bit_generator.pxd,sha256=lArpIXSgTwVnJMYc4XX0NGxegXq3h_QsUDK6qeZKbNc,1007
numpy/random/bit_generator.pyi,sha256=sXPTnGMgICncbhgGBPZvwTv2mcS4ENKB4G4PIhCqTaQ,3534
numpy/random/c_distributions.pxd,sha256=UCtqx0Nf-vHuJVaqPlLFURWnaI1vH-vJRE01BZDTL9o,6335
numpy/random/lib/libnpyrandom.a,sha256=-1eNSFrUGkCTqr47fgZpBAXE8Qa0kpQAYOlGJJctVWw,72270
numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so,sha256=u5WziPv8H4ejMrXAjdaw9WXHyafFpKfhjp2sKohKTrU,781008
numpy/random/mtrand.pyi,sha256=NUzAPLtDaft-xJlKUx4u1e3QwnofZbWgt2KEV8_GiAY,22018
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=Xkef402AVB-eZgYQkVtoxERHkxffCA9Jyt_oMbtJGwY,15844
numpy/random/tests/data/mt19937-testset-2.csv,sha256=nsBEQNnff-aFjHYK4thjvUK4xSXDSfv5aTbcE59pOkE,15825
numpy/random/tests/data/pcg64-testset-1.csv,sha256=xB00DpknGUTTCxDr9L6aNo9Hs-sfzEMbUSS4t11TTfE,23839
numpy/random/tests/data/pcg64-testset-2.csv,sha256=NTdzTKvG2U7_WyU_IoQUtMzU3kEvDH39CgnR6VzhTkw,23845
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=vNSUT-gXS_oEw_awR3O30ziVO4seNPUv1UIZ01SfVnI,23833
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=uylS8PU2AIKZ185OC04RBr_OePweGRtvn-dE4YN0yYA,23839
numpy/random/tests/data/philox-testset-1.csv,sha256=SedRaIy5zFadmk71nKrGxCFZ6BwKz8g1A9-OZp3IkkY,23852
numpy/random/tests/data/philox-testset-2.csv,sha256=dWECt-sbfvaSiK8-Ygp5AqyjoN5i26VEOrXqg01rk3g,23838
numpy/random/tests/data/sfc64-testset-1.csv,sha256=iHs6iX6KR8bxGwKk-3tedAdMPz6ZW8slDSUECkAqC8Q,23840
numpy/random/tests/data/sfc64-testset-2.csv,sha256=FIDIDFCaPZfWUSxsJMAe58hPNmMrU27kCd9FhCEYt_k,23833
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=Ce2wQHcNV33qnkeHbORji-SW55RnHQ2vUdGXK1YVJBk,19956
numpy/random/tests/test_extending.py,sha256=po8h6ASy9-C0LHPKKpjYyAokOSj_xKh9FeQAavb4GBA,4435
numpy/random/tests/test_generator_mt19937.py,sha256=Z7D8PciFoaYF_XCtINHzfjovWZHqgWmVOJ2UjvNkWlM,117288
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=r2wzyXTRfyVk__f2PO9yKPRdwx5ez671OQyAglMfPpc,8094
numpy/random/tests/test_random.py,sha256=i44DXCHEBtKtOzwSBfADh_kBSjMPgaCJYHdFfs6sfCQ,70150
numpy/random/tests/test_randomstate.py,sha256=Cp-op2kfopZ8wq-SBQ12Mh5RQ0p8mcBQHYSh0h-DegU,85275
numpy/random/tests/test_randomstate_regression.py,sha256=xS_HOwtijRdgq-gZn0IDUcm0NxdjjJXYv6ex8WN7FPU,7999
numpy/random/tests/test_regression.py,sha256=RbAzZYLfyzUKmup5uJR19sK2N17L_d1rLRy-CWjtIaQ,5462
numpy/random/tests/test_seed_sequence.py,sha256=GNRJ4jyzrtfolOND3gUWamnbvK6-b_p1bBK_RIG0sfU,3311
numpy/random/tests/test_smoke.py,sha256=CsXvEgv1T3wvCAH6qYu8RCWoQOaI4_gm7aWNhAS4QRg,28174
numpy/rec/__init__.py,sha256=w2G_npkmqm5vrWgds8V6Gusehmi1bRbiqCxsl9yOjow,83
numpy/rec/__init__.pyi,sha256=NWclXeZGtb9EvxymXj71lqOCKxcZPZawS-JJkc54_zQ,346
numpy/strings/__init__.py,sha256=-hT1HYpbswLkRWswieJQwAYn72IAwuaSCA5S1sdSPMk,83
numpy/strings/__init__.pyi,sha256=lDQvuJEXEx7Iw-8E-srZS6RkJzN19GQ_POsbyhFWMec,1295
numpy/testing/__init__.py,sha256=InpVKoDAzMKO_l_HNcatziW_u1k9_JZze__t2nybrL0,595
numpy/testing/__init__.pyi,sha256=1jr2Gj9BmCdtK4bqNGkwUAuqwC4n2JPOy6lqczK7xpA,2045
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/extbuild.py,sha256=fy4Dl-CqMtqBu6MShJNIe9DAuYH8kN_XZlUdOVcb1hQ,8106
numpy/testing/_private/extbuild.pyi,sha256=aNH6UnAhh4Zny81W45GrAcScB12b6_84y8M0Vdtpm2I,626
numpy/testing/_private/utils.py,sha256=UrWpMfsgQD54K46uj00dAVx1Pbl8JFCr0_yb7m1uZkQ,95700
numpy/testing/_private/utils.pyi,sha256=y4UuOhHLN9aThPfajrNL9Q86zqYmk03uB0Wv3MlOamo,12967
numpy/testing/overrides.py,sha256=IiVwsm3cDwnJdrk0FUFh7JLJYEnR_AfYWQRqWIeOFNQ,2133
numpy/testing/overrides.pyi,sha256=IQvQLxD-dHcbTQOZEO5bnCtCp8Uv3vj51dl0dZ0htjg,397
numpy/testing/print_coercion_tables.py,sha256=v9RlpFnOlaw34QGWnDIovDGhG1clwGhha0UnCqni0RE,6223
numpy/testing/print_coercion_tables.pyi,sha256=02D1q0WeMJ8B6txT_dy2Kn7IWse2RLRJQV0M6ifLD_w,821
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/test_utils.py,sha256=eMHfDFj21KcKuv8-aWhwdm3rHhIirtUkZJss-Qffggw,70456
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/test__all__.py,sha256=L3mCnYPTpzAgNfedVuq9g7xPWbc0c1Pot94k9jZ9NpI,221
numpy/tests/test_configtool.py,sha256=lhtwsoUPSOSdgnSdxvrvS4roiid86eWzSrGjdrKkH7g,1555
numpy/tests/test_ctypeslib.py,sha256=c0x56qlAMnxTCO9MiuV05LCoqju8cidHj1URV5gOwQE,12351
numpy/tests/test_lazyloading.py,sha256=R3Idpr9XIZ8C83sy8NvWSsh9knKxi42TAON13HpGRq0,1159
numpy/tests/test_matlib.py,sha256=gwhIXrJJo9DiecaGLCHLJBjhx2nVGl6yHq80AOUQSRM,1852
numpy/tests/test_numpy_config.py,sha256=x0OH4_gNx-13qw1_GYihFel1S4bWEzbrR_VT-H9x4tQ,1233
numpy/tests/test_numpy_version.py,sha256=2d0EtPJZYP3XRE6C6rfJW6QsPlFoDxqgO1yPxObaiE0,1754
numpy/tests/test_public_api.py,sha256=mG_c04GeGEue8ppN5G8djdNyVFe4vKUiBLoiO4h-dhU,27664
numpy/tests/test_reloading.py,sha256=sGu5XM-_VCNphyJcY5VCoQCmy5MgtL6_hDnsqf2j_ro,2367
numpy/tests/test_scripts.py,sha256=jluCLfG94VM1cuX-5RcLFBli_yaJZpIvmVuMxRKRJrc,1645
numpy/tests/test_warnings.py,sha256=HOqWSVu80PY-zacrgMfzPF0XPqEC24BNSw6Lmvw32Vg,2346
numpy/typing/__init__.py,sha256=ph9_WtDCJ7tKrbbRcz5OZEbXwxRXZfzSd2K1mLab910,5267
numpy/typing/mypy_plugin.py,sha256=eghgizS6dx7VuQiNbQg_cCfzNBb7Kyt3AomPNB8uml0,6470
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=OMnzSP_4S06yDMzOWeMS36r8Ew5EYzc0cFcr5JURf7c,3963
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=SjiwoGrefYsuScJcBQZlwzfvENADeRMxHYCDCo685Vc,1129
numpy/typing/tests/data/fail/array_like.pyi,sha256=V9lNwYrNOvOyHf5xtCZerofTXdubpvl1pzZMsEWD2U0,526
numpy/typing/tests/data/fail/array_pad.pyi,sha256=57oK0Yp53rtKjjIrRFYLcxa-IfIGhtI-bEem7ggJKwI,132
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=NZlON-sYl2s6-iUFKZZpXF4WnGVHQbTn8yIsQm0Alg4,586
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=Qb7oMI1GdDQO_jcoJEAsMkXLjzOdcb3sx-b5mW73cAE,470
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=gJ-ZL-e-yMbHMRKUv8r2KqJ08Mkgpg74nUe6lgi2RDU,583
numpy/typing/tests/data/fail/char.pyi,sha256=Zi3dygeaxHT8-5aFNCAreGU-T89zLg5pcE6c9NBCs6c,2712
numpy/typing/tests/data/fail/chararray.pyi,sha256=wdBMnihqJoeEMdSKz5Ur60qCDCVmiuHdTl4WmriTanc,2307
numpy/typing/tests/data/fail/comparisons.pyi,sha256=YrcL2POtM1g8GEWW4AJMl9vAkV-lG_6kEb7FzueeiLU,822
numpy/typing/tests/data/fail/constants.pyi,sha256=IzmswvmTKbAOkCjgyxu1jChlikIwqeAETHGVH2TtY0k,85
numpy/typing/tests/data/fail/datasource.pyi,sha256=gACpSdzMDej9WZbNvDQlkWX9DvHD7DjucesbH0EWEaM,405
numpy/typing/tests/data/fail/dtype.pyi,sha256=OAGABqdXNB8gClJFEGMckoycuZcIasMaAlS2RkiKROI,334
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=*******************************************,487
numpy/typing/tests/data/fail/flatiter.pyi,sha256=JcggwDkKcMWDBz0Ky8-dkJzjwnKxQ-kyea5br5DDqq0,866
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=NuOpn-kPy4g80PlAVVQZfhXwP6wITijvyTs0_uuzAyw,5703
numpy/typing/tests/data/fail/histograms.pyi,sha256=yAPVt0rYTwtxnigoGT-u7hhKCE9iYxsXc24x2HGBrmA,367
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=moINir9iQoi6Q1ZuVg5BuSB9hSBtbg_uzv-Qm_lLYZk,509
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=0FBv6CYJMDrL0U9cGsiO5a0boUrBCSB4eFHHLVjBzEo,2689
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=Ur7Y4iZX6WmoH5SDm0ePi8C8LPsuPs2Yr7g7P5O613g,899
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=6oI_kPhJqL0P0q-rsC3WtGso3V-hF7ntbNUmbhUPfXE,96
numpy/typing/tests/data/fail/lib_version.pyi,sha256=7-ZJDZwDcB-wzpMN8TeYtZAgaqc7xnQ8Dnx2ISiX2Ts,158
numpy/typing/tests/data/fail/linalg.pyi,sha256=yDd05aK1dI37RPt3pD2eJYo4dZFaT2yB1PEu3K0y9Tg,1322
numpy/typing/tests/data/fail/memmap.pyi,sha256=HSTCQYNuW1Y6X1Woj361pN4rusSPs4oDCXywqk20yUo,159
numpy/typing/tests/data/fail/modules.pyi,sha256=K73WuMJxw7zo3oALIcTuNfU4sPlKeGzEUxPlL1f97cM,621
numpy/typing/tests/data/fail/multiarray.pyi,sha256=1_9X7BW6hukiappz0kn3WCWN6OWXtT6OQqmJmJpdkfQ,1643
numpy/typing/tests/data/fail/ndarray.pyi,sha256=cgoWlpQqBQ5pkfiYsoz2f6o-DASrVRCraKBCgXLJQSk,404
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=H2bpfqfd04Syto7SLWJOq-gWmCCzRWJpLIiQVPI0qE0,1000
numpy/typing/tests/data/fail/nditer.pyi,sha256=w7emjnOxnf3NcvLktNLlke6Cuivn2gU3sVmGCfbG6rw,325
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=em4GZwLDFE0QSxxg081wVwhh-Dmtkn8f7wThI0DiXVs,427
numpy/typing/tests/data/fail/npyio.pyi,sha256=Jsl8KB55PwQ2Xz9jXtL3j-G1RIQLCcEuLJmO_o3hZBI,628
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=jl_pxMAq_VmkaK13-sfhUOUYGAQ4OV2pQ1d7wG-DNZg,120
numpy/typing/tests/data/fail/random.pyi,sha256=0sFOsJeHwYc1cUNF-MByWONEF_MP8CQWTjdyGFvgl90,2821
numpy/typing/tests/data/fail/rec.pyi,sha256=Ws3TyesnoQjt7Q0wwtpShRDJmZCs2jjP17buFMomVGA,704
numpy/typing/tests/data/fail/scalars.pyi,sha256=P_l-XImP_R7YQirkuv5aRmYaLgExJs8Djl0_mDbdKsk,2862
numpy/typing/tests/data/fail/shape.pyi,sha256=pSxiQ6Stq60xGFKOGZUsisxIO0y4inJ8UpKeio89K04,137
numpy/typing/tests/data/fail/shape_base.pyi,sha256=Y_f4buHtX2Q2ZA4kaDTyR8LErlPXTzCB_-jBoScGh_Q,152
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=IjA0Xrnx0lG3m07d1Hjbhtyo1Te5cXgjgr5fLUo4LYQ,315
numpy/typing/tests/data/fail/strings.pyi,sha256=AiH368QQsUT6JVWgePOei4TRpKGGT-3z2NvswSoRT_U,2370
numpy/typing/tests/data/fail/testing.pyi,sha256=xEUrFKLL8_gt3RV7d6NbF9a6zu2uaKcWBIN_pqGS_Ds,1343
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=eRFtqBbwkVI6G6MZMVpep1UKnFMDYzhrN82fO3ilnH0,898
numpy/typing/tests/data/fail/type_check.pyi,sha256=CIyI0j0Buxv0QgCvNG2urjaKpoIZ-ZNawC2m6NzGlbo,379
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=0t_yJ4eVOhneDSfa3EsoTh6RreyMtkHVOi9oQ35_EW0,734
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=JsJ3M8QZv9-6GKwRnojJGIfeIkdtJFe-3ix5reLXx-M,627
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=8N8m_GbRAH0bWjDEzYnH4MREX86iBD46Ug9mm-vc1co,476
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=KXExnFGz9O7Veut_U7YEIpi6x-BdfeaGtpqWf1Yd274,185
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=bS8bBeCFqjgtOiy-8_y39wfa7rwhdjLz2Vmo-RXAYD4,884
numpy/typing/tests/data/mypy.ini,sha256=6yPaDeYIVWc-WNRdSjAYOGlSVCWkmcge2Te8JAmhjpI,285
numpy/typing/tests/data/pass/arithmetic.py,sha256=e71PA71VJitjkF8wrmui2F2zoTt0iOCW2tfbCpNDYlQ,7447
numpy/typing/tests/data/pass/array_constructors.py,sha256=rfJ8SRB4raElxRjsHBCsZIkZAfqZMie0VE8sSKMgkHg,2447
numpy/typing/tests/data/pass/array_like.py,sha256=ddPI6pA27qnp1INWs4Yi3wCqoVypSRMxstO771WQS5c,1056
numpy/typing/tests/data/pass/arrayprint.py,sha256=y_KkuLz1uM7pv53qfq7GQOuud4LoXE3apK1wtARdVyM,766
numpy/typing/tests/data/pass/arrayterator.py,sha256=FqcpKdUQBQ0FazHFxr9MsLEZG-jnJVGKWZX2owRr4DQ,393
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=FmEs_sKaU9ox-5f0NU3_TRIv0XxLQVEZ8rou9VNehb4,964
numpy/typing/tests/data/pass/comparisons.py,sha256=5aGrNl3D7Yd1m9WVkHrjJtqi7SricTxrEMtmIV9x0aE,3298
numpy/typing/tests/data/pass/dtype.py,sha256=YDuYAb0oKoJc9eOnKJuoPfLbIKOgEdE04_CYxRS4U5I,1070
numpy/typing/tests/data/pass/einsumfunc.py,sha256=eXj5L5MWPtQHgrHPsJ36qqrmBHqct9UoujjJCvHnF1k,1370
numpy/typing/tests/data/pass/flatiter.py,sha256=0BnbuLMBC7MQlprNZ0QhNSscfYwPhEhXOhWoyiRACWU,174
numpy/typing/tests/data/pass/fromnumeric.py,sha256=d_hVLyrVDFPVx33aqLIyAGYYQ8XAJFIzrAsE8QCoof4,3991
numpy/typing/tests/data/pass/index_tricks.py,sha256=dmonWJMUKsXg23zD_mibEEtd4b5ys-sEfT9Fnnq08x8,1402
numpy/typing/tests/data/pass/lib_user_array.py,sha256=Za_n84msWtV8dqQZhMhvh7lzu5WZvO8ixTPkEqO2Hms,590
numpy/typing/tests/data/pass/lib_utils.py,sha256=bj1sEA4gsmezqbYdqKnVtKzY_fb64w7PEoZwNvaaUdA,317
numpy/typing/tests/data/pass/lib_version.py,sha256=HnuGOx7tQA_bcxFIJ3dRoMAR0fockxg4lGqQ4g7LGIw,299
numpy/typing/tests/data/pass/literal.py,sha256=WKT1I15Iw37bqkgBlY1h1_Kb_gs1Qme8Wy3wTr0op90,1504
numpy/typing/tests/data/pass/ma.py,sha256=slJZQFGPI4I13qc-CRfreEGhIUk4TdFk-Pv75yWanNM,171
numpy/typing/tests/data/pass/mod.py,sha256=owFL1fys3LPTWpAlsjS-IzW4sSu98ncp2BnsIetLSrA,1576
numpy/typing/tests/data/pass/modules.py,sha256=g9PhyLO6rflYHZtmryx1VWTubphN4TAPUSfoiYriTqE,625
numpy/typing/tests/data/pass/multiarray.py,sha256=MxHax6l94yqlTVZleAqG77ILEbW6wU5osPcHzxJ85ns,1331
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=d7cFNUrofdLXh9T_9RG3Esz1XOihWWQNlz5Lb0yt6dM,1525
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=om45RP2VtXvEhbprrJzh09S6OGQvlqrLi2B9JFTOKxc,3466
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=37eYwMNqMLwanIW9-63hrokacnSz2K_qtPUlkdpsTjo,640
numpy/typing/tests/data/pass/nditer.py,sha256=nYO45Lw3ZNbQq75Vht86zzLZ4cWzP3ml0rxDPlYt8_8,63
numpy/typing/tests/data/pass/numeric.py,sha256=wbmYMkK1LM34jjFek8VFJYyade_L6u7XqjpdqGyoRwU,1625
numpy/typing/tests/data/pass/numerictypes.py,sha256=6x6eN9-5NsSQUSc6rf3fYieS2poYEY0t_ujbwgF9S5Q,331
numpy/typing/tests/data/pass/random.py,sha256=UJF6epKYGfGq9QlrR9YuA7EK_mI8AQ2osdA4Uhsh1ms,61824
numpy/typing/tests/data/pass/recfunctions.py,sha256=_rcCY44c3LnxMFjoLcnOlVc9yXKbRUIY2nIkNoar9h4,5037
numpy/typing/tests/data/pass/scalars.py,sha256=OAfNg3VYmO-iSxQCSmY_OUyUjCwcRIKwiT-OR52FFP4,3725
numpy/typing/tests/data/pass/shape.py,sha256=0nyLAArcbN6JQQDqBhLkJ_nYj5z0zpQnaZLWIMPO8PQ,449
numpy/typing/tests/data/pass/simple.py,sha256=lPj620zkTA8Sg893eu2mGuj-Xq2BGZ_1dcmfsVDkz8g,2751
numpy/typing/tests/data/pass/simple_py3.py,sha256=HuLrc5aphThQkLjU2_19KgGFaXwKOfSzXe0p2xMm8ZI,96
numpy/typing/tests/data/pass/ufunc_config.py,sha256=uzXOhCl9N4LPV9hV2Iqg_skgkKMbBPBF0GXPU9EMeuE,1205
numpy/typing/tests/data/pass/ufunclike.py,sha256=U4Aay11VALvm22bWEX0eDWuN5qxJlg_hH5IpOL62M3I,1125
numpy/typing/tests/data/pass/ufuncs.py,sha256=1Rem_geEm4qyD3XaRA1NAPKwr3YjRq68zbIlC_Xhi9M,422
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=ETLZkDTGpZspvwjVYAZlnA1gH4PJ4bSY5PkWyxTjusU,161
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=pI3XrneSswKnOSa0a-9hsr7v9e4jDWO7v-gMQ81KLs4,25295
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=1LZSBV-FCdju6HBjBCJOLdcuMVuEdSN8-fkx-rldUZg,3047
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=RcwCSgtaDh4hjU7dcqoLb1tzxDp6vaifGosO3niJ33c,12573
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=m8yoSEuxGmbHDnTIXBN-ZHAI6rMEtre65Yk3Uopdogg,688
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=8I-_vItFAU5e4B6Ty9wsa_Y1Nzw3lh_EvmSokbClUW8,817
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=SlBlsdITj2PeaR_by03nysRHYPh3G9gkvvcj5cKgFWA,4424
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=LKnpHT_L3_qzzeAORwVlWCLtJoo_42GXN2ZHyuWx9T0,1069
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=sEVMpf-QBsTDAEaiM9obInASKTDRQLVk2Ej8DWN5nLY,5049
numpy/typing/tests/data/reveal/char.pyi,sha256=wzkpRgHWgv4wQ1_KMnjakWN3B_p283kHn8TmP5nYJTY,10846
numpy/typing/tests/data/reveal/chararray.pyi,sha256=mbUYgjsaPHUcsQsCXkUo8Fi3H6gB84hQEo4DaM0US_o,6651
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=iZeK0iGQIiYt1IULgT7S1tR_feHyGkaY8wUaO9KOK3o,7225
numpy/typing/tests/data/reveal/constants.pyi,sha256=rXWIPvzafsXTbyTNOYfbUlK_j5xiz3XFNIGIrl7aKQI,362
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=DIPa9-dZLtghcevcABNQ3hpWiiPqdbpA2TT7SmrWyJE,4737
numpy/typing/tests/data/reveal/datasource.pyi,sha256=ROEU-LBTqzDCV_afVI-cb4qdn0UFWvSj9pjHsArBQyE,613
numpy/typing/tests/data/reveal/dtype.pyi,sha256=YclNqAAyjzNK6YCMvuHJWmVDVu_Kr30l2vPqz4GSrm8,5213
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=BZZQikSpk-ePbbWkW2b1VO1_BXFlaqQt2d0BYKE7WTQ,1956
numpy/typing/tests/data/reveal/emath.pyi,sha256=CHRd-4151gruyI2sao65epcdtaLdnGzmHfF3MJFIeNc,2335
numpy/typing/tests/data/reveal/fft.pyi,sha256=lcl6ZRCWilYyynSB12HyTmGa0ZEKDIhKqMRrgOLisiM,1661
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=M4dnFct3SheA2EkpIrR3ECxP5pAjjnC5C5Aelkb6DAk,1377
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=44SIUac6GFQH-quhitXIU2AaFvFfEPubswM-yvIAw_c,14917
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=FP6d4LrkydJ7KRJ2tIfBvjKW0FyAio6XxIhKca8EJvs,1582
numpy/typing/tests/data/reveal/histograms.pyi,sha256=ttfsdZBRqQzIfujkhNHExs20tH8qtCwJv5Yc4EAUwlk,1287
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=jdU0xs46CnK8haxTqZ-Z-aONablqKeJrN5cQGxCw7bg,3271
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=EkWPmm41sgaDg7On5EkEKvTXsy74juuuxV36VdrCwtE,9877
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=Z2mFp-281D_zd5YbtgiliDTKk6akckOiLkOXbLnwPO4,5895
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=ysQO1QVJvj9Z5iTLW1z7xMJmNch2qwTGbHL77aVOHKw,448
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=9KSTL1-sf93KZmAFyc_xXTIufDMapHAfXHtXVR8gO-4,583
numpy/typing/tests/data/reveal/linalg.pyi,sha256=DvWeTqPSyO_OlSxnkZbJkkEV7igdd-iMvMju2Zd2z2w,6236
numpy/typing/tests/data/reveal/matrix.pyi,sha256=C1-xZV_MN3wSeRxiPOg3r2_kmOhYrMXCmJC_U4dVcDc,3048
numpy/typing/tests/data/reveal/memmap.pyi,sha256=UdYaTVuRbMceCVMozcMTzdZ5qRrplzvovHChCvW55jg,754
numpy/typing/tests/data/reveal/mod.pyi,sha256=vroL10xpg449us1stjWkWFLBF6kPt9vQbsR1IF17-Z4,7611
numpy/typing/tests/data/reveal/modules.pyi,sha256=zOe7G_ofnwwwPQMdkKjI3mwt-xIy1kN-DjvWLQvm0r8,1870
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=ZYxzWuPoPn88crNn92hINm07OFBRiSv2A2l26yi0w2I,7865
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=x6QK76bchnI-u4D6b0AFxcLp2Kvzv-BJCwUwe3NY9N4,587
numpy/typing/tests/data/reveal/ndarray_assignability.pyi,sha256=vEDA7m6QDxM_sAR2PyY19IUCmspR3Te-bdD50M-RhJM,2698
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=N635zekh5wJfvTIFFI5tNc4NQVkyLLna1Wy4auihJMM,3377
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=kmW4gdoV3TvOiO3jWKqoBnuiWp1tBunnsvs3Aggvf-4,7903
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=9mPnQJofJ8vh9WQwWqNFxgQ6_f9Mv9rEU3iDXWnfbbQ,1405
numpy/typing/tests/data/reveal/nditer.pyi,sha256=c9DdxgUOnm886w3f3L2trxHMyOF5s-w8_2DZHRdbhwM,1933
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=-o-5gOFUflvmU_pJRIfuKVP0xi4oxTAUYRPeHRtHiLk,646
numpy/typing/tests/data/reveal/npyio.pyi,sha256=vrJNIovhI6cCpV0XrdISixluzR83i3z0PKr5jk6PuNo,3523
numpy/typing/tests/data/reveal/numeric.pyi,sha256=9wq71fIj5gT5xMditz8zM79adfF1bvyfJJCy7DfKok0,6081
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=nIJSi3T3S5v9sOyvh0IgllEHjGLE-a1W0sMowo11-_A,1361
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=EwzpzZnJnqxbe7W6MR0xJC-kzTRR428pXJDE6MgoNd4,7999
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=T1c-C1-b0k0j61OnlrhTkWUN6Pftdaccw8bwGX7dDN0,10764
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=2h3B9w8TPr7Gypr0-s6ITeOZ3iQ4VDgpaKi5T440U_I,7128
numpy/typing/tests/data/reveal/random.pyi,sha256=TlY_xhK3U--2Q1KiEkErDOvIxAHaqafHnTKMA_rv6U0,104329
numpy/typing/tests/data/reveal/rec.pyi,sha256=ZvdqHUobAT4UeiogaAm_nVg27YSJRwUQwMtqJOJawT4,3776
numpy/typing/tests/data/reveal/scalars.pyi,sha256=e7J0o8MAE_Henqh6Zcwv24NgCKgOlvOQ95MUdptmDzA,6449
numpy/typing/tests/data/reveal/shape.pyi,sha256=r0y0iSyVabz6hnIRQFdomLV6yvPqiXrGm0pVtTmm1Eg,292
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=W1wxdfVHMxzuX-c0BcR3UJkDiE2re6ODypZjSX1nNnY,2046
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=J3Kagblme0GxDnJFW_v1M1Ak28Bdep-8P0LA3tl3KuA,1345
numpy/typing/tests/data/reveal/strings.pyi,sha256=hZepuR_eOIKB2Ja6sw7VQ1LpIDE0cu9Z38YmlVTG-8Q,9415
numpy/typing/tests/data/reveal/testing.pyi,sha256=-XIbu-GFxdz3AuiEa8Xwlq9n02eRbyJWacIgFwEtEYk,8483
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=x5EgqxESpsFtvBsuTY1EmoyHBCr9aTCeg133oBUQuv0,4299
numpy/typing/tests/data/reveal/type_check.pyi,sha256=H4d9guDEa4Hn_hty1Wy_R7q9UpACIzB2_pgoaHO4kZw,2711
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=tXala6x3dbwUI1S1yYPMo47oXti_1aX84ZHlrbI5WcI,1191
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=F223MWONHITGeiJcpik_eLp8s56U2EsfLy73W-luTzM,1233
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=Gf3782hgHd0-tW1bVhzJhJBSk9GvL-lki8vDpjblMFk,4819
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=quHpFR_zwWzV7WGpYDMzf7RkHbomqRYrc93JUB09tkg,460
numpy/typing/tests/test_isfile.py,sha256=77lnjlxFqhrIRfGpSrqmvIVwpo9VoOPGiS7rRQSdKT0,865
numpy/typing/tests/test_runtime.py,sha256=2qu8JEliITnZCBJ_QJpohacj_OQ08o73ixS2w2ooNXI,3275
numpy/typing/tests/test_typing.py,sha256=wfRq_DQZg99tsuEQElXDtfbSmEOjkzEVizQB0cVp8-I,8308
numpy/version.py,sha256=p_z6CLw9cwp3p9MewCe_U6lpWBLDU6Um3Qd9wUUbfXo,293
numpy/version.pyi,sha256=tgN523dUbCHUTYs0QYz0HKchUfFwI9HKgoJY20rkewM,388
